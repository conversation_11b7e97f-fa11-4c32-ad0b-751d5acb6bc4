<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:components="clr-namespace:y4eb_praktika.Components"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
		MinWidth="800" MinHeight="450" Width="1200" Height="700"
        x:Class="y4eb_praktika.MainWindow"
        Title="Агентство недвижимости - Главная">
	<DockPanel>
		<!-- Navigation Header -->
		<components:NavigationHeader DockPanel.Dock="Top"/>

		<!-- Main Content Area -->
		<Grid Classes="MainContent">
			<StackPanel VerticalAlignment="Center" HorizontalAlignment="Center" MaxWidth="800">
				<Label Classes="WelcomeTitle">Добро пожаловать!</Label>
				<TextBlock Classes="WelcomeText" Margin="20,0">
					Выберите раздел из навигационного меню выше для работы с системой управления недвижимостью.
					Здесь вы можете управлять агентами, клиентами, объектами недвижимости, потребностями, предложениями и сделками.
				</TextBlock>
			</StackPanel>
		</Grid>
	</DockPanel>
</Window>
