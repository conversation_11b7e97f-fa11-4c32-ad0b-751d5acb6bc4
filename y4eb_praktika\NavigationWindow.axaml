<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
		MinWidth="800" MinHeight="450"
        x:Class="y4eb_praktika.MainWindow"
        Title="y4eb_praktika">
	<DockPanel>
		<Grid DockPanel.Dock="Top">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*"/>
				<ColumnDefinition Width="*"/>
				<ColumnDefinition Width="*"/>
			</Grid.ColumnDefinitions>
		<Image Grid.Column="2" Classes="Logo"/>
		<Label Grid.Column="1" Classes="Header1">Навигация</Label>
		</Grid>
		<StackPanel VerticalAlignment="Center">
			<StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
			<Button x:Name="AgentButton" Click="AgentButton_Click">Агенты</Button>
			<Button x:Name="RealEstateButton" Click="RealEstateButton_Click">Недвижимость</Button>
			<Button x:Name="ClientButton" Click="ClientButton_Click">Клиенты</Button>
			</StackPanel>
			<StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
			<Button x:Name="DemandsButton" Click="DemandButton_Click">Потребности</Button>
			<Button x:Name="DealsButton" Click="DealsButton_Click">Сделки</Button>
			<Button x:Name="SupliesButton" Click="SupliesButton_Click">Предложения</Button>
			</StackPanel>
		</StackPanel>
	</DockPanel>
</Window>
