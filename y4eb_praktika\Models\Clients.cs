﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class Clients
    {
        [Key]
        public int Id { get; set; }

        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? LastName { get; set; }

        public string? Phone { get; set; }
        public string? Email { get; set; }

        public ICollection<Supplies> Supplies { get; set; }
        public ICollection<Demands> Demands { get; set; } 
    }
}
