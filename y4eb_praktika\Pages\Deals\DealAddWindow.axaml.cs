using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using System;
using System.Globalization;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Deals
{
    public partial class DealAddWindow : Window
    {
        private readonly WindowNotificationManager _notif;
        private const double DEFAULT_SHARE = 0.45;

        private y4eb_praktika.Models.Demands _selectedDemand;
        private y4eb_praktika.Models.Supplies _selectedSupply;

        private double _sellerComm, _buyerComm, _sellerAgent, _buyerAgent, _companyShare;

        public class LookupItem
        {
            public int Id { get; }
            public string Text { get; }
            public LookupItem(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        // Конструктор без параметров для XAML
        public DealAddWindow()
        {
            try
            {
                InitializeComponent();
                _notif = new WindowNotificationManager(this)
                {
                    Position = NotificationPosition.BottomLeft,
                    MaxItems = 3
                };
                LoadLookups();
            }
            catch (Exception ex)
            {
                if (_notif != null)
                {
                    _notif.Show(new Notification("Ошибка", $"Ошибка при инициализации: {ex.Message}", NotificationType.Error));
                }
                Close(false);
            }
        }

        private void LoadLookups()
        {
            try
            {
                using var db = MyDbContext.GetContext();

                var demandItems = db.Demands
                    .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                    .Where(d => d.Deal == null)
                    .Select(d => new LookupItem(
                        d.Id,
                        $"#{d.Id} - Клиент:{d.Client.MiddleName} {d.Client.LastName} - " +
                        $"Агент:{d.Agent.MiddleName} {d.Agent.LastName} - {d.Type.TypeName} - " +
                        $"[{d.MinPrice}-{d.MaxPrice}]"
                    ))
                    .ToList();
                DemandComboBox.ItemsSource = demandItems;

                var supplyItems = db.Supplies
                    .Include(s => s.Client).Include(s => s.Agent)
                    .Where(s => s.Deal == null)
                    .Select(s => new LookupItem(
                        s.Id,
                        $"#{s.Id} - Клиент:{s.Client.MiddleName} {s.Client.LastName} - " +
                        $"Агент:{s.Agent.MiddleName} {s.Agent.LastName} - Цена:{s.Price}"
                    ))
                    .ToList();
                SupplyComboBox.ItemsSource = supplyItems;
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при загрузке данных: {ex.Message}", NotificationType.Error));
                Close(false);
            }
        }

        private void ClearErrors()
        {
            DemandError.IsVisible = SupplyError.IsVisible = false;
        }

        private void SetError(TextBlock tb, string msg)
        {
            tb.Text = msg;
            tb.IsVisible = true;
        }

        private void CalcButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                ClearErrors();

                var demItem = DemandComboBox.SelectedItem as LookupItem;
                if (demItem == null)
                {
                    SetError(DemandError, "Выберите потребность");
                    return;
                }

                var supItem = SupplyComboBox.SelectedItem as LookupItem;
                if (supItem == null)
                {
                    SetError(SupplyError, "Выберите предложение");
                    return;
                }

                using var db = MyDbContext.GetContext();
                _selectedDemand = db.Demands
                    .Include(d => d.Type)
                    .Include(d => d.Agent)
                    .FirstOrDefault(d => d.Id == demItem.Id);
                    
                if (_selectedDemand == null)
                {
                    _notif.Show(new Notification("Ошибка", "Выбранная потребность не найдена", NotificationType.Error));
                    return;
                }

                _selectedSupply = db.Supplies
                    .Include(s => s.Agent)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                    .Include(s => s.RealEstate).ThenInclude(r => r.House)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                    .FirstOrDefault(s => s.Id == supItem.Id);
                    
                if (_selectedSupply == null)
                {
                    _notif.Show(new Notification("Ошибка", "Выбранное предложение не найдено", NotificationType.Error));
                    return;
                }

                int price = _selectedSupply.Price;

                // Проверяем, соответствует ли цена предложения диапазону цены потребности
                if (price < _selectedDemand.MinPrice || price > _selectedDemand.MaxPrice)
                {
                    _notif.Show(new Notification("Предупреждение", 
                        $"Цена предложения ({price}) не соответствует диапазону цены потребности [{_selectedDemand.MinPrice}-{_selectedDemand.MaxPrice}]", 
                        NotificationType.Warning));
                }

                // Расчёт комиссий
                var typeName = _selectedDemand.Type.TypeName;
                _sellerComm = typeName == "Квартира"
                    ? 36000 + 0.01 * price
                    : typeName == "Дом"
                        ? 30000 + 0.02 * price
                        : 30000 + 0.01 * price;
                _buyerComm = 0.03 * price;

                double shareS = (_selectedDemand.Agent.DealShare ?? (DEFAULT_SHARE * 100)) / 100.0;
                double shareB = (_selectedSupply.Agent.DealShare ?? (DEFAULT_SHARE * 100)) / 100.0;

                _sellerAgent = _sellerComm * shareS;
                _buyerAgent = _buyerComm * shareB;
                _companyShare = (_sellerComm - _sellerAgent) + (_buyerComm - _buyerAgent);

                SellerCommText.Text = _sellerComm.ToString("F2", CultureInfo.InvariantCulture);
                BuyerCommText.Text = _buyerComm.ToString("F2", CultureInfo.InvariantCulture);
                SellerAgentShareText.Text = _sellerAgent.ToString("F2", CultureInfo.InvariantCulture);
                BuyerAgentShareText.Text = _buyerAgent.ToString("F2", CultureInfo.InvariantCulture);
                CompanyShareText.Text = _companyShare.ToString("F2", CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при расчете комиссий: {ex.Message}", NotificationType.Error));
            }
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedDemand == null || _selectedSupply == null)
                {
                    _notif.Show(new Notification("Ошибка", "Выберите потребность и предложение", NotificationType.Error));
                    return;
                }

                using var db = MyDbContext.GetContext();
                
                // Проверяем, не связаны ли уже выбранные потребность и предложение с другими сделками
                var existingDeal = db.Deals
                    .FirstOrDefault(d => d.Demand_Id == _selectedDemand.Id || d.Supply_Id == _selectedSupply.Id);
                    
                if (existingDeal != null)
                {
                    _notif.Show(new Notification("Ошибка", 
                        "Выбранная потребность или предложение уже связаны с другой сделкой", 
                        NotificationType.Error));
                    return;
                }

                db.Deals.Add(new y4eb_praktika.Models.Deals
                {
                    Demand_Id = _selectedDemand.Id,
                    Supply_Id = _selectedSupply.Id
                });
                db.SaveChanges();

                _notif.Show(new Notification("Успех", "Новая сделка добавлена", NotificationType.Success));
                Close(true);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при сохранении: {ex.Message}", NotificationType.Error));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                Close(false);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при закрытии окна: {ex.Message}", NotificationType.Error));
            }
        }
    }
}
