﻿using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using System;
using System.Globalization;
using System.Linq;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Demands
{
    public partial class DemandAddWindow : Window
    {
        private readonly WindowNotificationManager _notif;

        public DemandAddWindow()
        {
            InitializeComponent();

            // Инициализируем менеджер уведомлений
            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            // Заполняем ComboBox-ы
            LoadLookups();
        }

        private void LoadLookups()
        {
            using var db = MyDbContext.GetContext();

            // Клиенты
            ClientComboBox.ItemsSource = db.Clients
                                     .Select(c => new { c.Id, Full = $"{c.MiddleName} {c.LastName}".Trim() })
                                     .ToList();
            ClientComboBox.SelectedIndex = -1;

            // Риэлторы
            AgentComboBox.ItemsSource = db.Agents
                                    .Select(a => new { a.Id, Full = $"{a.MiddleName} {a.LastName}".Trim() })
                                    .ToList();
            AgentComboBox.SelectedIndex = -1;

            // Типы
            TypeComboBox.ItemsSource = db.RealEstateTypes
                                   .Select(t => new { t.Id, Name = t.TypeName })
                                   .ToList();
            TypeComboBox.SelectedIndex = -1;
        }

        private void ClearErrors()
        {
            foreach (var tb in new[]
            {
                ClientError, AgentError, TypeError,
                MinPriceError, MaxPriceError,
                CityError, StreetError, HouseError, NumberError
            })
                tb.IsVisible = false;
        }

        private void SetError(TextBlock err, string msg)
        {
            err.Text = msg;
            err.IsVisible = true;
        }

        private bool TryParseInt(TextBox tb, TextBlock err, out int? val)
        {
            val = null;
            if (string.IsNullOrWhiteSpace(tb.Text)) return true;
            if (int.TryParse(tb.Text, out var v) && v >= 0)
            {
                val = v;
                return true;
            }
            SetError(err, "Введите положительное целое число");
            return false;
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            ClearErrors();
            bool hasError = false;

            // Выбор клиента
            var clientItem = ClientComboBox.SelectedItem as dynamic;
            if (clientItem == null) { SetError(ClientError, "Выберите клиента"); hasError = true; }

            // Выбор агента
            var agentItem = AgentComboBox.SelectedItem as dynamic;
            if (agentItem == null) { SetError(AgentError, "Выберите риэлтора"); hasError = true; }

            // Тип
            var typeItem = TypeComboBox.SelectedItem as dynamic;
            if (typeItem == null) { SetError(TypeError, "Выберите тип объекта"); hasError = true; }

            // Цены
            if (!TryParseInt(MinPriceTextBox, MinPriceError, out var minPrice)) hasError = true;
            if (!TryParseInt(MaxPriceTextBox, MaxPriceError, out var maxPrice)) hasError = true;
            if (minPrice.HasValue && maxPrice.HasValue && minPrice > maxPrice)
            {
                SetError(MaxPriceError, "Макс. цена ≥ Мин. цены");
                hasError = true;
            }

            // Адрес (необязательно, но пусть будет хотя бы город)
            var city = CityTextBox.Text?.Trim() ?? "";
            if (string.IsNullOrWhiteSpace(city))
            {
                SetError(CityError, "Введите город");
                hasError = true;
            }
            var street = StreetTextBox.Text?.Trim() ?? "";
            var house = HouseTextBox.Text?.Trim() ?? "";
            var number = NumberTextBox.Text?.Trim() ?? "";

            if (hasError) return;

            using var db = MyDbContext.GetContext();

            // создаём под-объект в зависимости от типа
            int? aptId = null, hdId = null, ldId = null;
            if (typeItem.Name == "Квартира")
            {
                var ad = new ApartmentsDemands
                {
                    AddressCity = city,
                    AddressStreet = street,
                    AddressHouse = house,
                    AddressNumber = number
                };
                db.ApartmentsDemands.Add(ad);
                db.SaveChanges();
                aptId = ad.Id;
            }
            else if (typeItem.Name == "Дом")
            {
                var hd = new HouseDemands
                {
                    Address_City = city,
                    Address_Street = street,
                    Address_House = house,
                    Address_Number = number
                };
                db.HouseDemands.Add(hd);
                db.SaveChanges();
                hdId = hd.Id;
            }
            else if (typeItem.Name == "Земля")
            {
                var ld = new LandDemands
                {
                    Address_City = city,
                    Address_Street = street,
                    Address_House = house,
                    Address_Number = number
                };
                db.LandDemands.Add(ld);
                db.SaveChanges();
                ldId = ld.Id;
            }

            var demand = new y4eb_praktika.Models.Demands
            {
                ClientId = (int)clientItem.Id,
                AgentId = (int)agentItem.Id,
                TypeId = (int)typeItem.Id,
                MinPrice = minPrice,
                MaxPrice = maxPrice,
                ApartmentDemandId = aptId,
                HouseDemandId = hdId,
                LandDemandId = ldId
            };
            db.Demands.Add(demand);
            db.SaveChanges();

            this.Close(true);
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            this.Close(false);
        }
    }
}
