<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="y4eb_praktika.Pages.RealEstate.RealEstateWindow"
		xmlns:controls="clr-namespace:y4eb_praktika"
        xmlns:models="clr-namespace:y4eb_praktika.Models"
        xmlns:components="clr-namespace:y4eb_praktika.Components"
        x:CompileBindings="False"
        MinWidth="800" MinHeight="450" Width="1200" Height="700"
        Title="Агентство недвижимости - Недвижимость">
	
	<DockPanel LastChildFill="True" Classes="PageContainer">
		<!-- Navigation Header -->
		<components:NavigationHeader DockPanel.Dock="Top"/>

		<!-- Page Header -->
		<Grid DockPanel.Dock="Top" Classes="PageHeader">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Click="BackButton_Click" Grid.Column="0" Margin="0,0,15,0">Назад</Button>
			<Button x:Name="AddButton" Click="AddButton_Click" Classes="outline-button" Grid.Column="1" Margin="0,0,15,0">Добавить недвижимость</Button>
			<Label Grid.Column="2" Classes="PageTitle">Недвижимость</Label>
		</Grid>

		<controls:SearchTextBox
		  x:Name="SearchBox"
		  Watermark="Поиск недвижимости"
		  DockPanel.Dock="Top"
		  Margin="20,0,20,15"/>

		<DataGrid x:Name="RealEstateGrid"
			  AutoGenerateColumns="False"
			  IsReadOnly="True"
			  SelectionMode="Single"
			  Margin="20,0,20,20"
			  Background="White"
			  BorderBrush="#E0E0E0"
			  BorderThickness="1"
			  CornerRadius="8">
			
			<DataGrid.Columns>
				<DataGridTextColumn Header="Тип" Binding="{Binding Type}" />
				<DataGridTextColumn Header="Город" Binding="{Binding City}" />
				<DataGridTextColumn Header="Улица" Binding="{Binding Street}" />
				<DataGridTextColumn Header="Дом" Binding="{Binding House}" />
				<DataGridTextColumn Header="Кв." Binding="{Binding ApartmentNumber}" />
				<DataGridTextColumn Header="Площадь" Binding="{Binding Area}" />
				<DataGridTextColumn Header="Комнаты" Binding="{Binding Rooms}" />
				<DataGridTextColumn Header="Этаж" Binding="{Binding Floor}" />
				<DataGridTextColumn Header="Этажей" Binding="{Binding TotalFloors}" />
				<DataGridTextColumn Header="Широта" Binding="{Binding Latitude}" />
				<DataGridTextColumn Header="Долгота" Binding="{Binding Longitude}" />
				<DataGridTemplateColumn Header="Изменить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Изменить"
									Classes="outline-button"
									Click="EditButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Удалить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Удалить"
									Classes="delete-button"
									Click="DeleteButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>
			</DataGrid.Columns>
		</DataGrid>
	</DockPanel>
	</Window>
