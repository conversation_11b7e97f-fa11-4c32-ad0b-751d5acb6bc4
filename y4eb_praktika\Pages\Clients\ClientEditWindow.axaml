<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        x:Class="y4eb_praktika.ClientEditWindow"
        Title="ClientEditWindow">
	<DockPanel LastChildFill="True">
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Назад</Button>
			<Label Grid.Column="1" Margin="10" FontSize="36" HorizontalAlignment="Center">Изменение клиентов</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>
		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="6">

				<Label>Id:</Label>
				<TextBlock x:Name="IdLabel"/>

				<Label>Фамилия:</Label>
				<TextBox x:Name="NameTextBox"/>
				<TextBlock x:Name="NameErrorTextBlock"
						   Classes="error-text"
						   IsVisible="False"/>

				<Label>Имя:</Label>
				<TextBox x:Name="MiddleNameTextBox"/>
				<TextBlock x:Name="MiddleNameErrorTextBlock"
						   Classes="error-text"
						   IsVisible="False"/>

				<Label>Отчество:</Label>
				<TextBox x:Name="LastNameTextBox"/>
				<TextBlock x:Name="LastNameErrorTextBlock"
						   Classes="error-text"
						   IsVisible="False"/>

				<Label>Номер телефона:</Label>
				<TextBox x:Name="PhoneNumberTextBox"/>
				<TextBlock x:Name="PhoneErrorTextBlock"
						   Classes="error-text"
						   IsVisible="False"/>

				<Label>Электронная почта:</Label>
				<TextBox x:Name="EmailTextBox"/>
				<TextBlock x:Name="EmailErrorTextBlock"
						   Classes="error-text"
						   IsVisible="False"/>

				<Button x:Name="SaveButton"
						Content="Сохранить"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
		</DockPanel>
</Window>
