using Avalonia.Controls;
using System.Diagnostics;
using y4eb_praktika.Pages.Deals;
using y4eb_praktika.Pages.Demands;
using y4eb_praktika.Pages.RealEstate;
using y4eb_praktika.Pages.Supplies;

namespace y4eb_praktika
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void ClientButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            ClientWindow clientWindow = new ClientWindow();
            clientWindow.Show();
            this.Close();
        }

        private void AgentButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            AgentsWindow agentsWindow = new AgentsWindow();
            agentsWindow.Show();
            this.Close();
        }
        private void RealEstateButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            RealEstateWindow realEstateWindow = new RealEstateWindow();
            realEstateWindow.Show();
            this.Close();
        }

        private void DemandButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            DemandsWindow demandsWindow = new DemandsWindow();
            demandsWindow.Show();
            this.Close();
        }

        private void SupliesButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            SuppliesWindow suppliesWindow = new SuppliesWindow();
            suppliesWindow.Show();
            this.Close();
        }
        private void DealsButton_Click(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            DealsWindow dealsWindow = new DealsWindow();
            dealsWindow.Show();
            this.Close();
        }
    }
}