﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class Agents
    {
        [Key]
        public int Id { get; set; }

        public string FirstName { get; set; } = string.Empty;
        public string MiddleName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;

        public double? DealShare { get; set; }

        public ICollection<Supplies> Supplies { get; set; }
        public ICollection<Demands> Demands { get; set; }

    }   
}
