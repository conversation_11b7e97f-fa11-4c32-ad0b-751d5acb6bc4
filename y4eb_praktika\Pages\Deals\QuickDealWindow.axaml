<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Width="800" Height="600"
        x:Class="y4eb_praktika.Pages.Deals.QuickDealWindow"
        x:CompileBindings="False"
        Title="Быстрое создание сделки">
    <DockPanel LastChildFill="True">
        <Grid DockPanel.Dock="Top" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Назад</Button>
            <Label Grid.Column="1" Margin="10" FontSize="24" HorizontalAlignment="Center">Быстрое создание сделки</Label>
        </Grid>

        <StackPanel Margin="20" Spacing="10">
            <StackPanel Orientation="Horizontal" Spacing="10" Margin="0,0,0,10">
                <RadioButton x:Name="SupplySearchMode" Content="Поиск по предложению" IsChecked="True" GroupName="SearchMode"/>
                <RadioButton x:Name="DemandSearchMode" Content="Поиск по потребности" GroupName="SearchMode"/>
            </StackPanel>

            <!-- Режим поиска по предложению -->
            <StackPanel x:Name="SupplySearchPanel" Spacing="10">
                <Label>Выберите предложение:</Label>
                <ComboBox x:Name="SupplyComboBox" Width="600"/>
                <TextBlock x:Name="SupplyError" Classes="error-text" IsVisible="False"/>

                <Label Margin="0,20,0,0">Подходящие потребности:</Label>
                <DataGrid x:Name="DemandGrid" AutoGenerateColumns="False" Height="250" Width="760" Margin="0,0,0,10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Клиент" Binding="{Binding Client}"/>
                        <DataGridTextColumn Header="Агент" Binding="{Binding Agent}"/>
                        <DataGridTextColumn Header="Мин. цена" Binding="{Binding MinPrice}"/>
                        <DataGridTextColumn Header="Макс. цена" Binding="{Binding MaxPrice}"/>
                        <DataGridTextColumn Header="Тип" Binding="{Binding TypeName}"/>
                        <DataGridTemplateColumn Header="Создать сделку">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="Создать сделку" Click="CreateDealButton_Click" Tag="{Binding}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>

            <!-- Режим поиска по потребности -->
            <StackPanel x:Name="DemandSearchPanel" Spacing="10" IsVisible="False">
                <Label>Выберите потребность:</Label>
                <ComboBox x:Name="DemandComboBox" Width="600"/>
                <TextBlock x:Name="DemandError" Classes="error-text" IsVisible="False"/>

                <Label Margin="0,20,0,0">Подходящие предложения:</Label>
                <DataGrid x:Name="SupplyGrid" AutoGenerateColumns="False" Height="250" Width="760" Margin="0,0,0,10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Клиент" Binding="{Binding Client}"/>
                        <DataGridTextColumn Header="Агент" Binding="{Binding Agent}"/>
                        <DataGridTextColumn Header="Цена" Binding="{Binding Price}"/>
                        <DataGridTextColumn Header="Тип" Binding="{Binding TypeName}"/>
                        <DataGridTemplateColumn Header="Создать сделку">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="Создать сделку" Click="CreateDealFromSupplyButton_Click" Tag="{Binding}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </StackPanel>
        </StackPanel>
    </DockPanel>
</Window> 