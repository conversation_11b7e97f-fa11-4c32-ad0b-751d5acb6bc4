<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Width="700" Height="600"
        x:Class="y4eb_praktika.Pages.Deals.DealAddWindow"
        Title="Создание сделки">
	<DockPanel LastChildFill="True">
		<!-- Заголовок -->
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto"/>
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Отмена</Button>
			<Label Grid.Column="1" Margin="10" FontSize="28" HorizontalAlignment="Center">Новая сделка</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>

		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="8">
				<!-- Выбор потребности -->
				<Label>Потребность:</Label>
				<ComboBox x:Name="DemandComboBox"/>
				<TextBlock x:Name="DemandError" Classes="error-text" IsVisible="False"/>

				<!-- Выбор предложения -->
				<Label>Предложение:</Label>
				<ComboBox x:Name="SupplyComboBox"/>
				<TextBlock x:Name="SupplyError" Classes="error-text" IsVisible="False"/>

				<!-- Кнопка расчёта -->
				<Button x:Name="CalcButton" Content="Рассчитать комиссии" Click="CalcButton_Click"/>

				<!-- Результаты расчёта -->
				<StackPanel Spacing="4" Margin="0,10">
					<TextBlock Text="Комиссия продавца:"/>
					<TextBlock x:Name="SellerCommText"/>
					<TextBlock Text="Комиссия покупателя:"/>
					<TextBlock x:Name="BuyerCommText"/>
					<TextBlock Text="Риэлтор-продавца:"/>
					<TextBlock x:Name="SellerAgentShareText"/>
					<TextBlock Text="Риэлтор-покупателя:"/>
					<TextBlock x:Name="BuyerAgentShareText"/>
					<TextBlock Text="Компания получит:"/>
					<TextBlock x:Name="CompanyShareText"/>
				</StackPanel>

				<!-- Сохранение -->
				<Button x:Name="SaveButton"
						Content="Сохранить сделку"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
	</DockPanel>
</Window>
