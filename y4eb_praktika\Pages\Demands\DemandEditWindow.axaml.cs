﻿using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using System;
using System.Globalization;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Demands
{
    public partial class DemandEditWindow : Window
    {
        private readonly DemandViewModel _vm;
        private readonly WindowNotificationManager _notif;

        public DemandEditWindow(DemandViewModel vm)
        {
            InitializeComponent();
            _vm = vm;
            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            LoadLookups();
            PopulateFields();
        }

        private void LoadLookups()
        {
            using var db = MyDbContext.GetContext();

            var clients = db.Clients
                .Select(c => new { c.Id, Full = $"{c.MiddleName} {c.LastName}".Trim() })
                .ToList();
            ClientComboBox.ItemsSource = clients;

            var agents = db.Agents
                .Select(a => new { a.Id, Full = $"{a.MiddleName} {a.LastName}".Trim() })
                .ToList();
            AgentComboBox.ItemsSource = agents;

            var types = db.RealEstateTypes
                .Select(t => new { t.Id, Name = t.TypeName })
                .ToList();
            TypeComboBox.ItemsSource = types;
        }

        private void PopulateFields()
        {
            IdLabel.Text = _vm.Id.ToString();

            foreach (dynamic it in ClientComboBox.ItemsSource!)
                if (it.Id == _vm.Id) 
                    ClientComboBox.SelectedItem = it;

            foreach (dynamic it in AgentComboBox.ItemsSource!)
                if (it.Full == _vm.AgentFullName)
                    AgentComboBox.SelectedItem = it;

            foreach (dynamic it in TypeComboBox.ItemsSource!)
                if (it.Name == _vm.Type)
                    TypeComboBox.SelectedItem = it;

            // Цены
            MinPriceTextBox.Text = _vm.MinPrice?.ToString() ?? "";
            MaxPriceTextBox.Text = _vm.MaxPrice?.ToString() ?? "";

            // Адрес
            CityTextBox.Text = _vm.AddressCity;
            StreetTextBox.Text = _vm.AddressStreet;
            HouseTextBox.Text = _vm.AddressHouse;
            NumberTextBox.Text = _vm.AddressNumber;
        }

        private void ClearErrors()
        {
            foreach (var tb in new[] { ClientError, AgentError, TypeError,
                                       MinPriceError, MaxPriceError,
                                       CityError, StreetError, HouseError, NumberError })
                tb.IsVisible = false;
        }

        private void SetError(TextBlock err, string msg)
        {
            err.Text = msg;
            err.IsVisible = true;
        }

        private bool TryParseInt(TextBox tb, TextBlock err, out int? val)
        {
            val = null;
            if (string.IsNullOrWhiteSpace(tb.Text)) return true;
            if (int.TryParse(tb.Text, out var v) && v >= 0)
            {
                val = v;
                return true;
            }
            SetError(err, "Введите положительное целое число");
            return false;
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            ClearErrors();
            bool hasError = false;

            dynamic clientIt = ClientComboBox.SelectedItem;
            if (clientIt == null) { SetError(ClientError, "Выберите клиента"); hasError = true; }

            dynamic agentIt = AgentComboBox.SelectedItem;
            if (agentIt == null) { SetError(AgentError, "Выберите риэлтора"); hasError = true; }

            dynamic typeIt = TypeComboBox.SelectedItem;
            if (typeIt == null) { SetError(TypeError, "Выберите тип объекта"); hasError = true; }

            if (!TryParseInt(MinPriceTextBox, MinPriceError, out var minP)) hasError = true;
            if (!TryParseInt(MaxPriceTextBox, MaxPriceError, out var maxP)) hasError = true;
            if (minP > maxP)
            {
                SetError(MaxPriceError, "Макс. цена ≥ Мин. цены");
                hasError = true;
            }

            var city = CityTextBox.Text?.Trim() ?? "";
            var street = StreetTextBox.Text?.Trim() ?? "";
            var house = HouseTextBox.Text?.Trim() ?? "";
            var num = NumberTextBox.Text?.Trim() ?? "";

            if (string.IsNullOrEmpty(city)) { SetError(CityError, "Введите город"); hasError = true; }

            if (hasError) return;

            using var db = MyDbContext.GetContext();
            var demand = db.Demands
                .Include(d => d.ApartmentDemand)
                .Include(d => d.HouseDemand)
                .Include(d => d.LandDemand)
                .FirstOrDefault(d => d.Id == _vm.Id);
            if (demand == null) return;

            demand.ClientId = (int)clientIt.Id;
            demand.AgentId = (int)agentIt.Id;
            demand.TypeId = (int)typeIt.Id;
            demand.MinPrice = minP;
            demand.MaxPrice = maxP;

            if (demand.ApartmentDemandId != null)
            {
                var ad = demand.ApartmentDemand!;
                ad.AddressCity = city;
                ad.AddressStreet = street;
                ad.AddressHouse = house;
                ad.AddressNumber = num;
            }
            else if (demand.HouseDemandId != null)
            {
                var hd = demand.HouseDemand!;
                hd.Address_City = city;
                hd.Address_Street = street;
                hd.Address_House = house;
                hd.Address_Number = num;
            }
            else if (demand.LandDemandId != null)
            {
                var ld = demand.LandDemand!;
                ld.Address_City = city;
                ld.Address_Street = street;
                ld.Address_House = house;
                ld.Address_Number = num;
            }

            db.SaveChanges();
            this.Close(true);
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            this.Close(false);
        }
    }
}
