<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="650"
        x:Class="y4eb_praktika.RealEstateEditWindow"
        Title="Изменение недвижимости">
	<DockPanel LastChildFill="True">
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Назад</Button>
			<Label Grid.Column="1" Margin="10" FontSize="28" HorizontalAlignment="Center">Изменение недвижимости</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>
		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="6">

				<Label>Id:</Label>
				<TextBlock x:Name="IdLabel"/>

				<Label>Тип недвижимости:</Label>
				<ComboBox x:Name="TypeComboBox" IsEnabled="False">
					<ComboBoxItem Content="Квартира" />
					<ComboBoxItem Content="Дом" />
					<ComboBoxItem Content="Земля" />
				</ComboBox>

				<Label>Город:</Label>
				<TextBox x:Name="CityTextBox"/>
				<TextBlock x:Name="CityErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Улица:</Label>
				<TextBox x:Name="StreetTextBox"/>
				<TextBlock x:Name="StreetErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Дом:</Label>
				<TextBox x:Name="HouseTextBox"/>
				<TextBlock x:Name="HouseErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Номер квартиры:</Label>
				<TextBox x:Name="ApartmentNumberTextBox"/>
				<TextBlock x:Name="ApartmentNumberErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Широта:</Label>
				<TextBox x:Name="LatitudeTextBox"/>
				<TextBlock x:Name="LatitudeErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Долгота:</Label>
				<TextBox x:Name="LongitudeTextBox"/>
				<TextBlock x:Name="LongitudeErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Площадь (кв.м):</Label>
				<TextBox x:Name="AreaTextBox"/>
				<TextBlock x:Name="AreaErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<Label>Количество комнат:</Label>
				<TextBox x:Name="RoomsTextBox"/>
				<TextBlock x:Name="RoomsErrorTextBlock" Classes="error-text" IsVisible="False"/>

				<StackPanel x:Name="FloorPanel" Spacing="6">
					<Label>Этаж:</Label>
					<TextBox x:Name="FloorTextBox"/>
					<TextBlock x:Name="FloorErrorTextBlock" Classes="error-text" IsVisible="False"/>
				</StackPanel>

				<StackPanel x:Name="TotalFloorsPanel" Spacing="6">
					<Label>Этажность дома:</Label>
					<TextBox x:Name="TotalFloorsTextBox"/>
					<TextBlock x:Name="TotalFloorsErrorTextBlock" Classes="error-text" IsVisible="False"/>
				</StackPanel>

				<Button x:Name="SaveButton"
						Content="Сохранить"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
	</DockPanel>
</Window>
