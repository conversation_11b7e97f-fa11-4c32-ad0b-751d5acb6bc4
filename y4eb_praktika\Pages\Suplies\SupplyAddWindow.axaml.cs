﻿using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using System;
using System.Globalization;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Supplies
{
    public partial class SupplyAddWindow : Window
    {
        private readonly WindowNotificationManager _notif;

        public SupplyAddWindow()
        {
            InitializeComponent();

            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            LoadLookups();
        }

        private void LoadLookups()
        {
            using var db = MyDbContext.GetContext();

            // Клиенты
            var clients = db.Clients
                .Select(c => new { c.Id, Full = $"{c.MiddleName} {c.LastName}".Trim() })
                .ToList(); // ← Материализуем здесь
            ClientComboBox.ItemsSource = clients;
            ClientComboBox.SelectedIndex = -1;

            // Риэлторы
            var agents = db.Agents
                .Select(a => new { a.Id, Full = $"{a.MiddleName} {a.LastName}".Trim() })
                .ToList(); // ← Материализуем здесь
            AgentComboBox.ItemsSource = agents;
            AgentComboBox.SelectedIndex = -1;

            // Недвижимость: каждую коллекцию сразу в память
            var apartments = db.RealEstates
                .Include(r => r.Apartment)
                .Where(r => r.Apartment != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"Квартира #{r.Apartment.Id}: {r.Apartment.Address_City}, {r.Apartment.Address_Street} {r.Apartment.Address_House}, кв.{r.Apartment.Address_Number}"
                })
                .ToList(); // ← Материализуем

            var houses = db.RealEstates
                .Include(r => r.House)
                .Where(r => r.House != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"Дом #{r.House.Id}: {r.House.Address_City}, {r.House.Address_Street} {r.House.Address_House}"
                })
                .ToList(); // ← Материализуем

            var lands = db.RealEstates
                .Include(r => r.Land)
                .Where(r => r.Land != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"Земля #{r.Land.Id}: {r.Land.Address_City}, {r.Land.Address_Street} {r.Land.Address_House}"
                })
                .ToList(); // ← Материализуем

            // Теперь Concat пройдет в памяти без ошибок
            var items = apartments
                .Concat(houses)
                .Concat(lands)
                .ToList();

            RealEstateComboBox.ItemsSource = items;
            RealEstateComboBox.SelectedIndex = -1;
        }

        private void ClearErrors()
        {
            foreach (var tb in new[]
            {
                ClientError, AgentError, RealEstateError, PriceError
            })
                tb.IsVisible = false;
        }

        private void SetError(TextBlock err, string msg)
        {
            err.Text = msg;
            err.IsVisible = true;
        }

        private bool TryParseInt(TextBox tb, TextBlock err, out int? val)
        {
            val = null;
            if (string.IsNullOrWhiteSpace(tb.Text))
            {
                SetError(err, "Введите цену");
                return false;
            }

            if (int.TryParse(tb.Text, NumberStyles.Integer, CultureInfo.InvariantCulture, out var v) && v > 0)
            {
                val = v;
                return true;
            }

            SetError(err, "Введите целое положительное число");
            return false;
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            ClearErrors();
            bool hasError = false;

            dynamic clientIt = ClientComboBox.SelectedItem;
            if (clientIt == null) { SetError(ClientError, "Выберите клиента"); hasError = true; }

            dynamic agentIt = AgentComboBox.SelectedItem;
            if (agentIt == null) { SetError(AgentError, "Выберите риэлтора"); hasError = true; }

            dynamic realIt = RealEstateComboBox.SelectedItem;
            if (realIt == null) { SetError(RealEstateError, "Выберите объект недвижимости"); hasError = true; }

            if (!TryParseInt(PriceTextBox, PriceError, out var price)) hasError = true;

            if (hasError) return;

            using var db = MyDbContext.GetContext();

            // проверка: если вошёл уже в сделку
            var re = db.RealEstates.Find((int)realIt.Id);
            if (re == null) { /* редкая ситуация */ }

            var supply = new y4eb_praktika.Models.Supplies
            {
                ClientId = (int)clientIt.Id,
                AgentId = (int)agentIt.Id,
                RealEstateId = (int)realIt.Id,
                Price = price!.Value
            };

            db.Supplies.Add(supply);
            db.SaveChanges();

            _notif.Show(new Notification("Успех", "Новое предложение добавлено", NotificationType.Success));
            this.Close(true);
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            this.Close(false);
        }
    }
}
