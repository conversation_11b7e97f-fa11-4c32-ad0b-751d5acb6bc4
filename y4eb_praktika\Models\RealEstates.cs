﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class RealEstates
    {
        [Key]
        public int Id {  get; set; }
        public int? ApartmentId { get; set; }
        public Apartments? Apartment { get; set; }

        public int? HouseId { get; set; }
        public Houses? House { get; set; }

        public int? LandId { get; set; }
        public Lands? Land { get; set; }

        public int? DistrictId { get; set; }
        public Districts? District { get; set; }
    }
}
