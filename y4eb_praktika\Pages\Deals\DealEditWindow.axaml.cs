using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Globalization;
using System.Linq;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Deals
{
    public partial class DealEditWindow : Window
    {
        private readonly WindowNotificationManager _notif;
        private const double DEFAULT_SHARE = 0.45;

        private y4eb_praktika.Models.Demands _demand;
        private y4eb_praktika.Models.Supplies _supply;

        private double _sellerComm, _buyerComm, _sellerAgent, _buyerAgent, _companyShare;

        public class LookupItem
        {
            public int Id { get; }
            public string Text { get; }
            public LookupItem(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        // Конструктор без параметров для XAML
        public DealEditWindow()
        {
            InitializeComponent();
            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            // Подписываемся на событие загрузки окна
            this.Loaded += DealEditWindow_Loaded;
        }

        private bool _isInitialized = false;
        private int _pendingDemandId = 0;
        private int _pendingSupplyId = 0;

        private void DealEditWindow_Loaded(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            // Если есть отложенная инициализация, выполняем её
            if (!_isInitialized && _pendingDemandId > 0 && _pendingSupplyId > 0)
            {
                InitializeInternal(_pendingDemandId, _pendingSupplyId);
            }
        }

        // Метод для инициализации данных после создания окна
        public void Initialize(int demandId, int supplyId)
        {
            _pendingDemandId = demandId;
            _pendingSupplyId = supplyId;

            // Если окно уже загружено, инициализируем сразу
            if (IsLoaded)
            {
                InitializeInternal(demandId, supplyId);
            }
            // Иначе инициализация произойдет в событии Loaded
        }

        private void InitializeInternal(int demandId, int supplyId)
        {
            if (_isInitialized) return;

            try
            {
                using var db = MyDbContext.GetContext();
                _demand = db.Demands
                            .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                            .FirstOrDefault(d => d.Id == demandId);
                _supply = db.Supplies
                            .Include(s => s.Client).Include(s => s.Agent)
                            .FirstOrDefault(s => s.Id == supplyId);

                if (_demand == null || _supply == null)
                {
                    _notif.Show(new Notification("Ошибка", "Потребность или предложение не найдены", NotificationType.Error));
                    Close(false);
                    return;
                }

                LoadLookups();
                PopulateFields();
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при инициализации: {ex.Message}", NotificationType.Error));
                Close(false);
            }
        }

        private void LoadLookups()
        {
            if (DemandComboBox == null || SupplyComboBox == null)
            {
                throw new InvalidOperationException("Элементы управления не инициализированы");
            }

            using var db = MyDbContext.GetContext();

            var demItem = new LookupItem(
                _demand.Id,
                $"#{_demand.Id} - Клиент:{_demand.Client?.MiddleName ?? ""} {_demand.Client?.LastName ?? ""} - " +
                $"Агент:{_demand.Agent?.MiddleName ?? ""} {_demand.Agent?.LastName ?? ""} - {_demand.Type?.TypeName ?? ""} - " +
                $"[{_demand.MinPrice}-{_demand.MaxPrice}]"
            );
            DemandComboBox.ItemsSource = new[] { demItem };

            var supplyItems = db.Supplies
                .Include(s => s.Client).Include(s => s.Agent)
                .Where(s => s.Deal == null || s.Id == _supply.Id)
                .Select(s => new LookupItem(
                    s.Id,
                    $"#{s.Id} - Клиент:{s.Client.MiddleName ?? ""} {s.Client.LastName ?? ""} - " +
                    $"Агент:{s.Agent.MiddleName ?? ""} {s.Agent.LastName ?? ""} - Цена:{s.Price}"
                ))
                .ToList();

            SupplyComboBox.ItemsSource = supplyItems;
        }

        private void PopulateFields()
        {
            if (DemandComboBox == null || SupplyComboBox == null)
            {
                throw new InvalidOperationException("Элементы управления не инициализированы");
            }

            DemandComboBox.SelectedIndex = 0;

            if (SupplyComboBox.ItemsSource != null)
            {
                var selected = SupplyComboBox.Items?.Cast<LookupItem>().FirstOrDefault(x => x.Id == _supply.Id);
                if (selected != null)
                    SupplyComboBox.SelectedItem = selected;
                else
                    _notif.Show(new Notification("Предупреждение", $"Предложение #{_supply.Id} не найдено в списке", NotificationType.Warning));
            }
        }

        private void ClearErrors()
        {
            DemandError.IsVisible = SupplyError.IsVisible = false;
        }

        private void SetError(TextBlock tb, string msg)
        {
            tb.Text = msg;
            tb.IsVisible = true;
        }

        private void CalcButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                ClearErrors();

                var sel = SupplyComboBox.SelectedItem as LookupItem;
                if (sel == null)
                {
                    SetError(SupplyError, "Выберите предложение");
                    return;
                }

                using var db = MyDbContext.GetContext();
                _supply = db.Supplies
                    .Include(s => s.Agent)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                    .Include(s => s.RealEstate).ThenInclude(r => r.House)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                    .FirstOrDefault(s => s.Id == sel.Id);

                if (_supply == null)
                {
                    _notif.Show(new Notification("Ошибка", "Предложение не найдено", NotificationType.Error));
                    return;
                }

                int price = _supply.Price;
                var typeName = _demand.Type.TypeName;

                _sellerComm = typeName == "Квартира"
                    ? 36000 + 0.01 * price
                    : typeName == "Дом"
                        ? 30000 + 0.02 * price
                        : 30000 + 0.01 * price;

                _buyerComm = 0.03 * price;

                double shareS = (_demand.Agent.DealShare ?? (DEFAULT_SHARE * 100)) / 100.0;
                double shareB = (_supply.Agent.DealShare ?? (DEFAULT_SHARE * 100)) / 100.0;

                _sellerAgent = _sellerComm * shareS;
                _buyerAgent = _buyerComm * shareB;
                _companyShare = (_sellerComm - _sellerAgent) + (_buyerComm - _buyerAgent);

                SellerCommText.Text = _sellerComm.ToString("F2", CultureInfo.InvariantCulture);
                BuyerCommText.Text = _buyerComm.ToString("F2", CultureInfo.InvariantCulture);
                SellerAgentShareText.Text = _sellerAgent.ToString("F2", CultureInfo.InvariantCulture);
                BuyerAgentShareText.Text = _buyerAgent.ToString("F2", CultureInfo.InvariantCulture);
                CompanyShareText.Text = _companyShare.ToString("F2", CultureInfo.InvariantCulture);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при расчете комиссий: {ex.Message}", NotificationType.Error));
            }
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                var sel = SupplyComboBox.SelectedItem as LookupItem;
                if (sel == null)
                {
                    _notif.Show(new Notification("Ошибка", "Выберите предложение", NotificationType.Error));
                    return;
                }

                using var db = MyDbContext.GetContext();
                var deal = db.Deals.FirstOrDefault(d => d.Demand_Id == _demand.Id);
                if (deal == null)
                {
                    _notif.Show(new Notification("Ошибка", "Сделка не найдена", NotificationType.Error));
                    return;
                }

                deal.Supply_Id = sel.Id;
                db.SaveChanges();

                _notif.Show(new Notification("Успех", "Сделка обновлена", NotificationType.Success));
                Close(true);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при сохранении: {ex.Message}", NotificationType.Error));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            try
            {
                Close(false);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при закрытии окна: {ex.Message}", NotificationType.Error));
            }
        }
    }
}
