<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <Design.PreviewWith>
    <Border Padding="20">
		<Button>Hello World!</Button>
    </Border>
  </Design.PreviewWith>
	<Style Selector="Button">
		<Setter Property="Background" Value="#0091ea"/>
		<Setter Property="Foreground" Value="White"/>
		<Setter Property="FontSize" Value="16"/>
		<Setter Property="Padding" Value="10"/>
		<Setter Property="Margin" Value="10"/>
		<Setter Property="CornerRadius" Value="4"/>
		<Setter Property="FontFamily" Value="Roboto"/>
	</Style>
	<Style Selector="Button:pointerover /template/ ContentPresenter">
		<Setter Property="Background" Value="#0081d1"/>
		<Setter Property="Foreground" Value="White"/>

	</Style>
	<Style Selector="Button:pressed /template/ ContentPresenter">
		<Setter Property="Background" Value="#005A95"/>
		<Setter Property="Foreground" Value="White"/>
	</Style>
</Styles>
