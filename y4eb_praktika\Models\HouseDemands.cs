﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class HouseDemands
    {
        [Key]
        public int Id { get; set; }
        public string? Address_City { get; set; }
        public string? Address_Street { get; set; }
        public string? Address_House { get; set; }
        public string? Address_Number { get; set; }

        public int? MinFloors { get; set; }
        public int? MaxFloors { get; set; }

        public int? MinRooms { get; set; }
        public int? MaxRooms { get; set; }

        public double? Min<PERSON>rea { get; set; }
        public double? MaxArea { get; set; }
    }
}
