<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Width="600" Height="650"
        x:Class="y4eb_praktika.Pages.Demands.DemandEditWindow"
        Title="Изменение потребности">
	<DockPanel LastChildFill="True">
		<!-- Верхняя панель -->
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Назад</Button>
			<Label Grid.Column="1" Margin="10" FontSize="28" HorizontalAlignment="Center">Изменение потребности</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>

		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="8">
				<!-- Id -->
				<Label>Id:</Label>
				<TextBlock x:Name="IdLabel"/>

				<!-- Клиент -->
				<Label>Клиент:</Label>
				<ComboBox x:Name="ClientComboBox" />
				<TextBlock x:Name="ClientError" Classes="error-text" IsVisible="False"/>

				<!-- Риэлтор -->
				<Label>Риэлтор:</Label>
				<ComboBox x:Name="AgentComboBox" />
				<TextBlock x:Name="AgentError" Classes="error-text" IsVisible="False"/>

				<!-- Тип -->
				<Label>Тип объекта:</Label>
				<ComboBox x:Name="TypeComboBox" />
				<TextBlock x:Name="TypeError" Classes="error-text" IsVisible="False"/>

				<!-- Мин/макс цена -->
				<Label>Мин. цена:</Label>
				<TextBox x:Name="MinPriceTextBox"/>
				<TextBlock x:Name="MinPriceError" Classes="error-text" IsVisible="False"/>

				<Label>Макс. цена:</Label>
				<TextBox x:Name="MaxPriceTextBox"/>
				<TextBlock x:Name="MaxPriceError" Classes="error-text" IsVisible="False"/>

				<!-- Адрес -->
				<Label>Город:</Label>
				<TextBox x:Name="CityTextBox"/>
				<TextBlock x:Name="CityError" Classes="error-text" IsVisible="False"/>

				<Label>Улица:</Label>
				<TextBox x:Name="StreetTextBox"/>
				<TextBlock x:Name="StreetError" Classes="error-text" IsVisible="False"/>

				<Label>Дом:</Label>
				<TextBox x:Name="HouseTextBox"/>
				<TextBlock x:Name="HouseError" Classes="error-text" IsVisible="False"/>

				<Label>Кв./Участок:</Label>
				<TextBox x:Name="NumberTextBox"/>
				<TextBlock x:Name="NumberError" Classes="error-text" IsVisible="False"/>

				<!-- Кнопка сохранить -->
				<Button x:Name="SaveButton"
						Content="Сохранить"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
	</DockPanel>
</Window>
