﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class Demands
    {
        [Key]
        public int Id {  get; set; }
        public int? MinPrice { get; set; }
        public int? MaxPrice { get; set; }

        public int AgentId { get; set; }
        public Agents Agent { get; set; } = null!;

        public int ClientId { get; set; }
        public Clients Client { get; set; } = null!;

        public int? ApartmentDemandId { get; set; }
        public ApartmentsDemands? ApartmentDemand { get; set; }

        public int? HouseDemandId { get; set; }
        public HouseDemands? HouseDemand { get; set; }

        public int? LandDemandId { get; set; }
        public LandDemands? LandDemand { get; set; }

        public int TypeId { get; set; }
        public RealEstateTypes Type { get; set; }

        public Deals? Deal { get; set; }
    }
}
