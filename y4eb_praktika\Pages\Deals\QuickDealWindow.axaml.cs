using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Deals
{
    public partial class QuickDealWindow : Window
    {
        private readonly WindowNotificationManager _notif;
        public ObservableCollection<DemandViewModel> Demands { get; } = new();
        public ObservableCollection<SupplyViewModel> Supplies { get; } = new();

        public class SupplyLookup
        {
            public int Id { get; }
            public string Text { get; }
            public SupplyLookup(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        public class DemandLookup
        {
            public int Id { get; }
            public string Text { get; }
            public DemandLookup(int id, string text) { Id = id; Text = text; }
            public override string ToString() => Text;
        }

        public class DemandViewModel
        {
            public int Id { get; set; }
            public string Client { get; set; } = "";
            public string Agent { get; set; } = "";
            public int? MinPrice { get; set; }
            public int? MaxPrice { get; set; }
            public string TypeName { get; set; } = "";
        }

        public class SupplyViewModel
        {
            public int Id { get; set; }
            public string Client { get; set; } = "";
            public string Agent { get; set; } = "";
            public int Price { get; set; }
            public string TypeName { get; set; } = "";
        }

        public QuickDealWindow()
        {
            InitializeComponent();
            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            // Инициализация обработчиков событий
            SupplySearchMode.IsCheckedChanged += SearchMode_Changed;
            DemandSearchMode.IsCheckedChanged += SearchMode_Changed;
            SupplyComboBox.SelectionChanged += SupplyComboBox_SelectionChanged;
            DemandComboBox.SelectionChanged += DemandComboBox_SelectionChanged;

            // Загрузка начальных данных
            LoadSupplies();
            LoadDemands();
            DemandGrid.ItemsSource = Demands;
            SupplyGrid.ItemsSource = Supplies;
        }

        private void SearchMode_Changed(object? sender, Avalonia.Interactivity.RoutedEventArgs e)
        {
            if (sender is RadioButton rb)
            {
                SupplySearchPanel.IsVisible = rb == SupplySearchMode;
                DemandSearchPanel.IsVisible = rb == DemandSearchMode;
            }
        }

        private void LoadSupplies()
        {
            using var db = MyDbContext.GetContext();
            var supplies = db.Supplies
                .Include(s => s.Client).Include(s => s.Agent)
                .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                .Include(s => s.RealEstate).ThenInclude(r => r.House)
                .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                .Where(s => s.Deal == null)
                .Select(s => new SupplyLookup(
                    s.Id,
                    $"#{s.Id} - Клиент:{s.Client.MiddleName} {s.Client.LastName} - " +
                    $"Агент:{s.Agent.MiddleName} {s.Agent.LastName} - Цена:{s.Price}"
                ))
                .ToList();
            SupplyComboBox.ItemsSource = supplies;
        }

        private void LoadDemands()
        {
            using var db = MyDbContext.GetContext();
            var demands = db.Demands
                .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                .Where(d => d.Deal == null)
                .Select(d => new DemandLookup(
                    d.Id,
                    $"#{d.Id} - Клиент:{d.Client.MiddleName} {d.Client.LastName} - " +
                    $"Агент:{d.Agent.MiddleName} {d.Agent.LastName} - {d.Type.TypeName} - " +
                    $"[{d.MinPrice}-{d.MaxPrice}]"
                ))
                .ToList();
            DemandComboBox.ItemsSource = demands;
        }

        private void SupplyComboBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            Demands.Clear();
            SupplyError.IsVisible = false;
            var selected = SupplyComboBox.SelectedItem as SupplyLookup;
            if (selected == null)
                return;

            try
            {
                using var db = MyDbContext.GetContext();
                var supply = db.Supplies
                    .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                    .Include(s => s.RealEstate).ThenInclude(r => r.House)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                    .FirstOrDefault(s => s.Id == selected.Id);
                if (supply?.RealEstate == null)
                {
                    _notif.Show(new Notification("Ошибка", "Предложение или недвижимость не найдены", NotificationType.Error));
                    return;
                }

                // Определяем тип недвижимости предложения для отладки
                string supplyTypeName = "";
                if (supply.RealEstate.Apartment != null) supplyTypeName = "Квартира";
                else if (supply.RealEstate.House != null) supplyTypeName = "Дом";
                else if (supply.RealEstate.Land != null) supplyTypeName = "Участок";

                if (string.IsNullOrEmpty(supplyTypeName))
                {
                    _notif.Show(new Notification("Предупреждение", "Не удалось определить тип недвижимости", NotificationType.Warning));
                    return;
                }

                // Получаем все потребности (без фильтрации по типу, так как будем проверять по связанным объектам)
                var allDemands = db.Demands
                    .Include(d => d.Client).Include(d => d.Agent).Include(d => d.Type)
                    .Include(d => d.ApartmentDemand)
                    .Include(d => d.HouseDemand)
                    .Include(d => d.LandDemand)
                    .Where(d => d.Deal == null)
                    .ToList();

                // Фильтруем по детальным критериям
                var matchingDemands = allDemands.Where(d => IsSupplyMatchesDemand(supply, d)).ToList();

                foreach (var d in matchingDemands)
                {
                    Demands.Add(new DemandViewModel
                    {
                        Id = d.Id,
                        Client = $"{d.Client?.MiddleName ?? ""} {d.Client?.LastName ?? ""}".Trim(),
                        Agent = $"{d.Agent?.MiddleName ?? ""} {d.Agent?.LastName ?? ""}".Trim(),
                        MinPrice = d.MinPrice,
                        MaxPrice = d.MaxPrice,
                        TypeName = d.Type?.TypeName ?? "Неизвестно"
                    });
                }

                if (matchingDemands.Count == 0)
                {
                    // Подсчитаем потребности по типам для отладки
                    int apartmentDemands = allDemands.Count(d => d.ApartmentDemand != null);
                    int houseDemands = allDemands.Count(d => d.HouseDemand != null);
                    int landDemands = allDemands.Count(d => d.LandDemand != null);

                    string debugInfo = $"Всего потребностей: {allDemands.Count} (квартиры: {apartmentDemands}, дома: {houseDemands}, участки: {landDemands}). Предложение: {supplyTypeName}";
                    _notif.Show(new Notification("Информация", $"Не найдено подходящих потребностей для предложения #{supply.Id}. {debugInfo}", NotificationType.Information));
                }
                else
                {
                    _notif.Show(new Notification("Успех", $"Найдено {matchingDemands.Count} подходящих потребностей из {allDemands.Count} доступных", NotificationType.Success));
                }
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при поиске потребностей: {ex.Message}", NotificationType.Error));
            }
        }

        private void DemandComboBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            Supplies.Clear();
            DemandError.IsVisible = false;
            var selected = DemandComboBox.SelectedItem as DemandLookup;
            if (selected == null)
                return;

            try
            {
                using var db = MyDbContext.GetContext();
                var demand = db.Demands
                    .Include(d => d.Type)
                    .Include(d => d.ApartmentDemand)
                    .Include(d => d.HouseDemand)
                    .Include(d => d.LandDemand)
                    .FirstOrDefault(d => d.Id == selected.Id);
                if (demand == null)
                {
                    _notif.Show(new Notification("Ошибка", "Потребность не найдена", NotificationType.Error));
                    return;
                }

                // Получаем все предложения (без предварительной фильтрации по типу)
                var allSupplies = db.Supplies
                    .Include(s => s.Client).Include(s => s.Agent)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Apartment)
                    .Include(s => s.RealEstate).ThenInclude(r => r.House)
                    .Include(s => s.RealEstate).ThenInclude(r => r.Land)
                    .Where(s => s.Deal == null)
                    .ToList();

                // Фильтруем по детальным критериям (тип определяется по связанным объектам)
                var matchingSupplies = allSupplies.Where(s => IsDemandMatchesSupply(demand, s)).ToList();

                foreach (var s in matchingSupplies)
                {
                    string typeName = s.RealEstate?.Apartment != null ? "Квартира" :
                                    s.RealEstate?.House != null ? "Дом" :
                                    s.RealEstate?.Land != null ? "Участок" : "Неизвестно";

                    Supplies.Add(new SupplyViewModel
                    {
                        Id = s.Id,
                        Client = $"{s.Client?.MiddleName ?? ""} {s.Client?.LastName ?? ""}".Trim(),
                        Agent = $"{s.Agent?.MiddleName ?? ""} {s.Agent?.LastName ?? ""}".Trim(),
                        Price = s.Price,
                        TypeName = typeName
                    });
                }

                if (matchingSupplies.Count == 0)
                {
                    // Подсчитаем предложения по типам для отладки
                    int apartmentSupplies = allSupplies.Count(s => s.RealEstate?.Apartment != null);
                    int houseSupplies = allSupplies.Count(s => s.RealEstate?.House != null);
                    int landSupplies = allSupplies.Count(s => s.RealEstate?.Land != null);

                    string demandType = demand.ApartmentDemand != null ? "квартира" :
                                       demand.HouseDemand != null ? "дом" :
                                       demand.LandDemand != null ? "участок" : "неизвестно";

                    string debugInfo = $"Всего предложений: {allSupplies.Count} (квартиры: {apartmentSupplies}, дома: {houseSupplies}, участки: {landSupplies}). Потребность: {demandType}";
                    _notif.Show(new Notification("Информация", $"Не найдено подходящих предложений для потребности #{demand.Id}. {debugInfo}", NotificationType.Information));
                }
                else
                {
                    _notif.Show(new Notification("Успех", $"Найдено {matchingSupplies.Count} подходящих предложений из {allSupplies.Count} доступных", NotificationType.Success));
                }
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при поиске предложений: {ex.Message}", NotificationType.Error));
            }
        }

        private async void CreateDealButton_Click(object? sender, RoutedEventArgs e)
        {
            var selectedSupply = SupplyComboBox.SelectedItem as SupplyLookup;
            if (selectedSupply == null)
            {
                SupplyError.Text = "Выберите предложение";
                SupplyError.IsVisible = true;
                return;
            }
            if (sender is not Button btn || btn.Tag is not DemandViewModel demand)
                return;

            await CreateDeal(demand.Id, selectedSupply.Id);
        }

        private async void CreateDealFromSupplyButton_Click(object? sender, RoutedEventArgs e)
        {
            var selectedDemand = DemandComboBox.SelectedItem as DemandLookup;
            if (selectedDemand == null)
            {
                DemandError.Text = "Выберите потребность";
                DemandError.IsVisible = true;
                return;
            }
            if (sender is not Button btn || btn.Tag is not SupplyViewModel supply)
                return;

            await CreateDeal(selectedDemand.Id, supply.Id);
        }

        private async Task CreateDeal(int demandId, int supplyId)
        {
            try
            {
                using var db = MyDbContext.GetContext();
                // Проверка, не заняты ли уже
                var existingDeal = db.Deals.FirstOrDefault(d => d.Demand_Id == demandId || d.Supply_Id == supplyId);
                if (existingDeal != null)
                {
                    _notif.Show(new Notification("Ошибка", "Потребность или предложение уже участвуют в другой сделке", NotificationType.Error));
                    return;
                }
                db.Deals.Add(new y4eb_praktika.Models.Deals { Demand_Id = demandId, Supply_Id = supplyId });
                db.SaveChanges();
                _notif.Show(new Notification("Успех", "Сделка создана", NotificationType.Success));
                Close(true);
            }
            catch (Exception ex)
            {
                _notif.Show(new Notification("Ошибка", $"Ошибка при создании сделки: {ex.Message}", NotificationType.Error));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            Close(false);
        }

        /// <summary>
        /// Проверяет, соответствует ли предложение критериям потребности
        /// </summary>
        private bool IsSupplyMatchesDemand(y4eb_praktika.Models.Supplies supply, y4eb_praktika.Models.Demands demand)
        {
            // Проверка цены
            if (demand.MinPrice.HasValue && supply.Price < demand.MinPrice.Value) return false;
            if (demand.MaxPrice.HasValue && supply.Price > demand.MaxPrice.Value) return false;

            // Проверка типа недвижимости по связанным объектам потребности
            if (demand.ApartmentDemand != null && supply.RealEstate?.Apartment != null)
            {
                return IsApartmentMatches(supply.RealEstate.Apartment, demand.ApartmentDemand);
            }
            else if (demand.HouseDemand != null && supply.RealEstate?.House != null)
            {
                return IsHouseMatches(supply.RealEstate.House, demand.HouseDemand);
            }
            else if (demand.LandDemand != null && supply.RealEstate?.Land != null)
            {
                return IsLandMatches(supply.RealEstate.Land, demand.LandDemand);
            }

            return false;
        }

        /// <summary>
        /// Проверяет, соответствует ли потребность критериям предложения (обратная проверка)
        /// </summary>
        private bool IsDemandMatchesSupply(y4eb_praktika.Models.Demands demand, y4eb_praktika.Models.Supplies supply)
        {
            return IsSupplyMatchesDemand(supply, demand);
        }

        /// <summary>
        /// Проверяет соответствие квартиры критериям потребности
        /// </summary>
        private bool IsApartmentMatches(y4eb_praktika.Models.Apartments apartment, y4eb_praktika.Models.ApartmentsDemands demand)
        {
            // Проверка адреса - только если указан в потребности
            if (!string.IsNullOrWhiteSpace(demand.AddressCity))
            {
                if (string.IsNullOrWhiteSpace(apartment.Address_City) ||
                    !apartment.Address_City.Contains(demand.AddressCity, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.AddressStreet))
            {
                if (string.IsNullOrWhiteSpace(apartment.Address_Street) ||
                    !apartment.Address_Street.Contains(demand.AddressStreet, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.AddressHouse))
            {
                if (string.IsNullOrWhiteSpace(apartment.Address_House) ||
                    !apartment.Address_House.Contains(demand.AddressHouse, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.AddressNumber))
            {
                if (string.IsNullOrWhiteSpace(apartment.Address_Number) ||
                    !apartment.Address_Number.Contains(demand.AddressNumber, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            // Проверка площади - только если указана в потребности
            if (demand.MinArea.HasValue)
            {
                if (!apartment.TotalArea.HasValue || apartment.TotalArea.Value < demand.MinArea.Value)
                    return false;
            }
            if (demand.MaxArea.HasValue)
            {
                if (!apartment.TotalArea.HasValue || apartment.TotalArea.Value > demand.MaxArea.Value)
                    return false;
            }

            // Проверка количества комнат - только если указано в потребности
            if (demand.MinRooms.HasValue)
            {
                if (!apartment.Rooms.HasValue || apartment.Rooms.Value < demand.MinRooms.Value)
                    return false;
            }
            if (demand.MaxRooms.HasValue)
            {
                if (!apartment.Rooms.HasValue || apartment.Rooms.Value > demand.MaxRooms.Value)
                    return false;
            }

            // Проверка этажа - только если указан в потребности
            if (demand.MinFloor.HasValue)
            {
                if (!apartment.Floor.HasValue || apartment.Floor.Value < demand.MinFloor.Value)
                    return false;
            }
            if (demand.MaxFloor.HasValue)
            {
                if (!apartment.Floor.HasValue || apartment.Floor.Value > demand.MaxFloor.Value)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Проверяет соответствие дома критериям потребности
        /// </summary>
        private bool IsHouseMatches(y4eb_praktika.Models.Houses house, y4eb_praktika.Models.HouseDemands demand)
        {
            // Проверка адреса - только если указан в потребности
            if (!string.IsNullOrWhiteSpace(demand.Address_City))
            {
                if (string.IsNullOrWhiteSpace(house.Address_City) ||
                    !house.Address_City.Contains(demand.Address_City, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_Street))
            {
                if (string.IsNullOrWhiteSpace(house.Address_Street) ||
                    !house.Address_Street.Contains(demand.Address_Street, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_House))
            {
                if (string.IsNullOrWhiteSpace(house.Address_House) ||
                    !house.Address_House.Contains(demand.Address_House, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_Number))
            {
                if (string.IsNullOrWhiteSpace(house.Address_Number) ||
                    !house.Address_Number.Contains(demand.Address_Number, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            // Проверка площади - только если указана в потребности
            if (demand.MinArea.HasValue)
            {
                if (!house.TotalArea.HasValue || house.TotalArea.Value < demand.MinArea.Value)
                    return false;
            }
            if (demand.MaxArea.HasValue)
            {
                if (!house.TotalArea.HasValue || house.TotalArea.Value > demand.MaxArea.Value)
                    return false;
            }

            // Проверка количества комнат - только если указано в потребности
            if (demand.MinRooms.HasValue)
            {
                if (!house.Rooms.HasValue || house.Rooms.Value < demand.MinRooms.Value)
                    return false;
            }
            if (demand.MaxRooms.HasValue)
            {
                if (!house.Rooms.HasValue || house.Rooms.Value > demand.MaxRooms.Value)
                    return false;
            }

            // Проверка этажности - только если указана в потребности
            if (demand.MinFloors.HasValue)
            {
                if (!house.TotalFloors.HasValue || house.TotalFloors.Value < demand.MinFloors.Value)
                    return false;
            }
            if (demand.MaxFloors.HasValue)
            {
                if (!house.TotalFloors.HasValue || house.TotalFloors.Value > demand.MaxFloors.Value)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Проверяет соответствие участка критериям потребности
        /// </summary>
        private bool IsLandMatches(y4eb_praktika.Models.Lands land, y4eb_praktika.Models.LandDemands demand)
        {
            // Проверка адреса - только если указан в потребности
            if (!string.IsNullOrWhiteSpace(demand.Address_City))
            {
                if (string.IsNullOrWhiteSpace(land.Address_City) ||
                    !land.Address_City.Contains(demand.Address_City, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_Street))
            {
                if (string.IsNullOrWhiteSpace(land.Address_Street) ||
                    !land.Address_Street.Contains(demand.Address_Street, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_House))
            {
                if (string.IsNullOrWhiteSpace(land.Address_House) ||
                    !land.Address_House.Contains(demand.Address_House, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            if (!string.IsNullOrWhiteSpace(demand.Address_Number))
            {
                if (string.IsNullOrWhiteSpace(land.Address_Number) ||
                    !land.Address_Number.Contains(demand.Address_Number, StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            // Проверка площади - только если указана в потребности
            if (demand.MinArea.HasValue)
            {
                if (!land.TotalArea.HasValue || land.TotalArea.Value < demand.MinArea.Value)
                    return false;
            }
            if (demand.MaxArea.HasValue)
            {
                if (!land.TotalArea.HasValue || land.TotalArea.Value > demand.MaxArea.Value)
                    return false;
            }

            return true;
        }
    }
}