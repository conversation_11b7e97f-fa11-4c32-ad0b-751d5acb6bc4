<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Width="600" Height="550"
        x:Class="y4eb_praktika.Pages.Deals.DealEditWindow"
        Title="Изменение сделки">
	<DockPanel LastChildFill="True">
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Отмена</Button>
			<Label Grid.Column="1" Margin="10" FontSize="28" HorizontalAlignment="Center">Изменить сделку</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>

		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="8">

				<Label>Потребность:</Label>
				<ComboBox x:Name="DemandComboBox" IsEnabled="False"/>
				<TextBlock x:Name="DemandError" Classes="error-text" IsVisible="False"/>

				<Label>Предложение:</Label>
				<ComboBox x:Name="SupplyComboBox"/>
				<TextBlock x:Name="SupplyError" Classes="error-text" IsVisible="False"/>

				<Button x:Name="CalcButton" Content="Пересчитать комиссии" Click="CalcButton_Click"/>

				<Label>Комиссия продавца:</Label>
				<TextBlock x:Name="SellerCommText"/>

				<Label>Комиссия покупателя:</Label>
				<TextBlock x:Name="BuyerCommText"/>

				<Label>Отчисление риэлтору (продавец):</Label>
				<TextBlock x:Name="SellerAgentShareText"/>

				<Label>Отчисление риэлтору (покупатель):</Label>
				<TextBlock x:Name="BuyerAgentShareText"/>

				<Label>Остаток компании:</Label>
				<TextBlock x:Name="CompanyShareText"/>

				<Button x:Name="SaveButton"
						Content="Сохранить"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
	</DockPanel>
</Window>
