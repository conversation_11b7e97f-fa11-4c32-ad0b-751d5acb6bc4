<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Width="600" Height="450"
        x:Class="y4eb_praktika.Pages.Supplies.SupplyAddWindow"
        Title="Добавление предложения">
	<DockPanel LastChildFill="True">
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto"/>
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Grid.Column="0" Margin="10" Click="BackButton_Click">Назад</Button>
			<Label Grid.Column="1" Margin="10" FontSize="28" HorizontalAlignment="Center">Добавление предложения</Label>
			<Image Grid.Column="2" Classes="Logo"/>
		</Grid>

		<ScrollViewer VerticalScrollBarVisibility="Auto">
			<StackPanel Margin="20" Spacing="8">

				<Label>Клиент:</Label>
				<ComboBox x:Name="ClientComboBox"/>
				<TextBlock x:Name="ClientError" Classes="error-text" IsVisible="False"/>

				<Label>Риэлтор:</Label>
				<ComboBox x:Name="AgentComboBox"/>
				<TextBlock x:Name="AgentError" Classes="error-text" IsVisible="False"/>

				<Label>Недвижимость:</Label>
				<ComboBox x:Name="RealEstateComboBox"/>
				<TextBlock x:Name="RealEstateError" Classes="error-text" IsVisible="False"/>

				<Label>Цена:</Label>
				<TextBox x:Name="PriceTextBox"/>
				<TextBlock x:Name="PriceError" Classes="error-text" IsVisible="False"/>

				<Button x:Name="SaveButton"
						Content="Сохранить"
						HorizontalAlignment="Center"
						Click="SaveButton_Click"/>
			</StackPanel>
		</ScrollViewer>
	</DockPanel>
</Window>
