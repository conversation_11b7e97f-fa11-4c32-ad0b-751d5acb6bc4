is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net8.0
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = y4eb_praktika
build_property.ProjectDir = D:\MyDiffFiles\КИТ\3 курс\2 семестр\y4eb_praktika\y4eb_praktika\y4eb_praktika\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Components/NavigationHeader.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/NavigationWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Agents/AgentAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Agents/AgentEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Agents/AgentsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Clients/ClientAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Clients/ClientEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Clients/ClientWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Deals/DealAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Deals/DealEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Deals/DealsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Deals/QuickDealWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Demands/DemandAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Demands/DemandEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Demands/DemandsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/RealEstate/RealEstateAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/RealEstate/RealEstateEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/RealEstate/RealEstateWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Suplies/SuppliesWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Suplies/SupplyAddWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Pages/Suplies/SupplyEditWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/DefButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/DefImages.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/DeleteButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/ErrorText.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/ErrorTextBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/Header.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/NavButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/OutlineButton.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[D:/MyDiffFiles/КИТ/3 курс/2 семестр/y4eb_praktika/y4eb_praktika/y4eb_praktika/Styles/SearchTextBox.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
