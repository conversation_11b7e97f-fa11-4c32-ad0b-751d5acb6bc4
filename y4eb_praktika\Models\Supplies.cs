﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace y4eb_praktika.Models
{
    public class Supplies
    {
        [Key]
        public int Id { get; set; }
        public int Price { get; set; }

        public int AgentId { get; set; }
        public Agents Agent { get; set; } = null!;

        public int ClientId { get; set; }
        public Clients Client { get; set; } = null!;

        public int RealEstateId { get; set; }
        public RealEstates RealEstate { get; set; } = null!;

        public Deals? Deal { get; set; }
    }
}
