<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
	<Design.PreviewWith>
		<Border Padding="20">
			<Button Classes="delete-button">Hello World!</Button>
		</Border>
	</Design.PreviewWith>
	<Style Selector="Button.delete-button">
		<Setter Property="Background" Value="#ff1744"/>
		<Setter Property="Foreground" Value="White"/>
		<Setter Property="FontSize" Value="16"/>
		<Setter Property="Padding" Value="10"/>
		<Setter Property="Margin" Value="10"/>
		<Setter Property="CornerRadius" Value="4"/>
		<Setter Property="FontFamily" Value="Roboto"/>
	</Style>
	<Style Selector="Button.delete-button:pointerover /template/ ContentPresenter">
		<Setter Property="Background" Value="#e3002c"/>
		<Setter Property="Foreground" Value="White"/>

	</Style>
	<Style Selector="Button.delete-button:pressed /template/ ContentPresenter">
		<Setter Property="Background" Value="#ff4a6d"/>
		<Setter Property="Foreground" Value="White"/>

	</Style>
</Styles>
