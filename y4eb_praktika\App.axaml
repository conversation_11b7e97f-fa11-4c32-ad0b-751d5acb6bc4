<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="y4eb_praktika.App"
             RequestedThemeVariant="Light">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->
    <Application.Styles>
        <FluentTheme />
		<StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/DefButton.axaml"/>
    	<StyleInclude Source="avares://y4eb_praktika/Styles/OutlineButton.axaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/DeleteButton.axaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/DefImages.axaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/Header.axaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/ErrorTextBox.axaml"/>
		<StyleInclude Source="avares://y4eb_praktika/Styles/ErrorText.axaml"/>
	</Application.Styles>
</Application>