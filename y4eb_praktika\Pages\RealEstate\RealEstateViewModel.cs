﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Pages.RealEstate
{
    public class RealEstateViewModel
    {
        public int Id { get; set; }
        public string Type { get; set; } = string.Empty; // "Квартира", "Дом", "Земля"

        public string? City { get; set; }
        public string? Street { get; set; }
        public string? House { get; set; }
        public string? ApartmentNumber { get; set; }

        public double? Latitude { get; set; }
        public double? Longitude { get; set; }

        public double? Area { get; set; }
        public int? Rooms { get; set; }
        public int? Floor { get; set; }         // для квартир
        public int? TotalFloors { get; set; }   // для домов
    }

}
