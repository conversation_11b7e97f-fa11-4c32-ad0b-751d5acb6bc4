﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class ApartmentsDemands
    {
        [Key]
        public int Id { get; set; }

        public float? Min<PERSON>rea { get; set; }
        public float? MaxArea { get; set; }

        public int? MinRooms { get; set; }
        public int? MaxRooms { get; set; }

        public int? MinFloor { get; set; }
        public int? MaxFloor { get; set; }

        public string? AddressCity { get; set; }
        public string? AddressStreet { get; set; }
        public string? AddressHouse { get; set; }
        public string? AddressNumber { get; set; }
    }

}
