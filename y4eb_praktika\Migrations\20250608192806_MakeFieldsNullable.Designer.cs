﻿// <auto-generated />
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using y4eb_praktika.Models;

#nullable disable

namespace y4eb_praktika.Migrations
{
    [DbContext(typeof(MyDbContext))]
    [Migration("20250608192806_MakeFieldsNullable")]
    partial class MakeFieldsNullable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("y4eb_praktika.Models.Agents", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<double?>("DealShare")
                        .HasColumnType("float");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiddleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Agents");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Apartments", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address_City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_House")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Coordinate_latitude")
                        .HasColumnType("float");

                    b.Property<double?>("Coordinate_longitude")
                        .HasColumnType("float");

                    b.Property<int?>("Floor")
                        .HasColumnType("int");

                    b.Property<int?>("Rooms")
                        .HasColumnType("int");

                    b.Property<double?>("TotalArea")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("Apartments");
                });

            modelBuilder.Entity("y4eb_praktika.Models.ApartmentsDemands", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AddressCity")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressHouse")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressStreet")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("MaxArea")
                        .HasColumnType("real");

                    b.Property<int>("MaxFloor")
                        .HasColumnType("int");

                    b.Property<int>("MaxRooms")
                        .HasColumnType("int");

                    b.Property<float>("MinArea")
                        .HasColumnType("real");

                    b.Property<int>("MinFloor")
                        .HasColumnType("int");

                    b.Property<int>("MinRooms")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("ApartmentsDemands");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Clients", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiddleName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Deals", b =>
                {
                    b.Property<int>("Demand_Id")
                        .HasColumnType("int");

                    b.Property<int>("Supply_Id")
                        .HasColumnType("int");

                    b.HasKey("Demand_Id");

                    b.HasIndex("Supply_Id")
                        .IsUnique();

                    b.ToTable("Deals");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Demands", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AgentId")
                        .HasColumnType("int");

                    b.Property<int?>("ApartmentDemandId")
                        .HasColumnType("int");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<int?>("HouseDemandId")
                        .HasColumnType("int");

                    b.Property<int?>("LandDemandId")
                        .HasColumnType("int");

                    b.Property<int?>("MaxPrice")
                        .HasColumnType("int");

                    b.Property<int?>("MinPrice")
                        .HasColumnType("int");

                    b.Property<int>("TypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.HasIndex("ApartmentDemandId");

                    b.HasIndex("ClientId");

                    b.HasIndex("HouseDemandId");

                    b.HasIndex("LandDemandId");

                    b.HasIndex("TypeId");

                    b.ToTable("Demands");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Districts", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<float?>("Area")
                        .HasColumnType("real");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Districts");
                });

            modelBuilder.Entity("y4eb_praktika.Models.HouseDemands", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address_City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_House")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("MaxArea")
                        .HasColumnType("float");

                    b.Property<int?>("MaxFloors")
                        .HasColumnType("int");

                    b.Property<int?>("MaxRooms")
                        .HasColumnType("int");

                    b.Property<double?>("MinArea")
                        .HasColumnType("float");

                    b.Property<int?>("MinFloors")
                        .HasColumnType("int");

                    b.Property<int?>("MinRooms")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("HouseDemands");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Houses", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address_City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_House")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Coordinate_latitude")
                        .HasColumnType("float");

                    b.Property<double?>("Coordinate_longitude")
                        .HasColumnType("float");

                    b.Property<int?>("Rooms")
                        .HasColumnType("int");

                    b.Property<double?>("TotalArea")
                        .HasColumnType("float");

                    b.Property<int?>("TotalFloors")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Houses");
                });

            modelBuilder.Entity("y4eb_praktika.Models.LandDemands", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address_City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_House")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("MaxArea")
                        .HasColumnType("float");

                    b.Property<double?>("MinArea")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("LandDemands");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Lands", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address_City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_House")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address_Street")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("Coordinate_latitude")
                        .HasColumnType("float");

                    b.Property<double?>("Coordinate_longitude")
                        .HasColumnType("float");

                    b.Property<double?>("TotalArea")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.ToTable("Lands");
                });

            modelBuilder.Entity("y4eb_praktika.Models.RealEstateTypes", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RealEstateTypes");
                });

            modelBuilder.Entity("y4eb_praktika.Models.RealEstates", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ApartmentId")
                        .HasColumnType("int");

                    b.Property<int?>("DistrictId")
                        .HasColumnType("int");

                    b.Property<int?>("HouseId")
                        .HasColumnType("int");

                    b.Property<int?>("LandId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ApartmentId");

                    b.HasIndex("DistrictId");

                    b.HasIndex("HouseId");

                    b.HasIndex("LandId");

                    b.ToTable("RealEstates");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Supplies", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AgentId")
                        .HasColumnType("int");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<int>("Price")
                        .HasColumnType("int");

                    b.Property<int>("RealEstateId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AgentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("RealEstateId");

                    b.ToTable("Supplies");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Deals", b =>
                {
                    b.HasOne("y4eb_praktika.Models.Demands", "Demand")
                        .WithOne("Deal")
                        .HasForeignKey("y4eb_praktika.Models.Deals", "Demand_Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("y4eb_praktika.Models.Supplies", "Supply")
                        .WithOne("Deal")
                        .HasForeignKey("y4eb_praktika.Models.Deals", "Supply_Id")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Demand");

                    b.Navigation("Supply");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Demands", b =>
                {
                    b.HasOne("y4eb_praktika.Models.Agents", "Agent")
                        .WithMany("Demands")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("y4eb_praktika.Models.ApartmentsDemands", "ApartmentDemand")
                        .WithMany()
                        .HasForeignKey("ApartmentDemandId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.Clients", "Client")
                        .WithMany("Demands")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("y4eb_praktika.Models.HouseDemands", "HouseDemand")
                        .WithMany()
                        .HasForeignKey("HouseDemandId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.LandDemands", "LandDemand")
                        .WithMany()
                        .HasForeignKey("LandDemandId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.RealEstateTypes", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agent");

                    b.Navigation("ApartmentDemand");

                    b.Navigation("Client");

                    b.Navigation("HouseDemand");

                    b.Navigation("LandDemand");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("y4eb_praktika.Models.RealEstates", b =>
                {
                    b.HasOne("y4eb_praktika.Models.Apartments", "Apartment")
                        .WithMany()
                        .HasForeignKey("ApartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.Districts", "District")
                        .WithMany()
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.Houses", "House")
                        .WithMany()
                        .HasForeignKey("HouseId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("y4eb_praktika.Models.Lands", "Land")
                        .WithMany()
                        .HasForeignKey("LandId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Apartment");

                    b.Navigation("District");

                    b.Navigation("House");

                    b.Navigation("Land");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Supplies", b =>
                {
                    b.HasOne("y4eb_praktika.Models.Agents", "Agent")
                        .WithMany("Supplies")
                        .HasForeignKey("AgentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("y4eb_praktika.Models.Clients", "Client")
                        .WithMany("Supplies")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("y4eb_praktika.Models.RealEstates", "RealEstate")
                        .WithMany()
                        .HasForeignKey("RealEstateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Agent");

                    b.Navigation("Client");

                    b.Navigation("RealEstate");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Agents", b =>
                {
                    b.Navigation("Demands");

                    b.Navigation("Supplies");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Clients", b =>
                {
                    b.Navigation("Demands");

                    b.Navigation("Supplies");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Demands", b =>
                {
                    b.Navigation("Deal");
                });

            modelBuilder.Entity("y4eb_praktika.Models.Supplies", b =>
                {
                    b.Navigation("Deal");
                });
#pragma warning restore 612, 618
        }
    }
}
