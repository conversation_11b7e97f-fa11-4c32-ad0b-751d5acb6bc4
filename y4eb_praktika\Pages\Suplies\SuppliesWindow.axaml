<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        xmlns:controls="clr-namespace:y4eb_praktika"
        xmlns:vm="clr-namespace:y4eb_praktika.Pages.Supplies"
        xmlns:components="clr-namespace:y4eb_praktika.Components"
        x:Class="y4eb_praktika.Pages.Supplies.SuppliesWindow"
		x:CompileBindings="False"
        Title="Агентство недвижимости - Предложения"
        MinWidth="800" MinHeight="450" Width="1200" Height="700">
	<DockPanel LastChildFill="True" Classes="PageContainer">
		<!-- Navigation Header -->
		<components:NavigationHeader DockPanel.Dock="Top"/>

		<!-- Page Header -->
		<Grid DockPanel.Dock="Top" Classes="PageHeader">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Click="BackButton_Click" Grid.Column="0" Margin="0,0,15,0">Назад</Button>
			<Button x:Name="AddButton" Click="AddButton_Click" Classes="outline-button" Grid.Column="1" Margin="0,0,15,0">Добавить предложение</Button>
			<Label Grid.Column="2" Classes="PageTitle">Предложения</Label>
		</Grid>

		<!-- Поиск по клиенту -->
		<controls:SearchTextBox
		  x:Name="SearchBox"
		  Watermark="Поиск по клиенту"
		  DockPanel.Dock="Top"
		  Margin="20,0,20,15"/>

		<!-- Таблица -->
		<DataGrid x:Name="SuppliesGrid"
				  AutoGenerateColumns="False"
				  IsReadOnly="True"
				  SelectionMode="Single"
				  Margin="20,0,20,20"
				  Background="White"
				  BorderBrush="#E0E0E0"
				  BorderThickness="1"
				  CornerRadius="8">
			<DataGrid.Columns>
				<DataGridTextColumn Header="Клиент"        Binding="{Binding ClientFullName}" />
				<DataGridTextColumn Header="Риэлтор"       Binding="{Binding AgentFullName}" />
				<DataGridTextColumn Header="Недвижимость"  Binding="{Binding RealEstateInfo}" Width="Auto" />
				<DataGridTextColumn Header="Цена"          Binding="{Binding Price}" />

				<DataGridTemplateColumn Header="Изменить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Изменить"
									Classes="outline-button"
									Click="EditButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Удалить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Удалить"
									Classes="delete-button"
									Click="DeleteButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>
			</DataGrid.Columns>
		</DataGrid>
	</DockPanel>
</Window>
