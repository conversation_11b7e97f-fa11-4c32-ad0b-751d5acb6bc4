<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        xmlns:controls="clr-namespace:y4eb_praktika"
        xmlns:vm="clr-namespace:y4eb_praktika.Pages.Supplies"
        x:Class="y4eb_praktika.Pages.Supplies.SuppliesWindow"
		x:CompileBindings="False"
        Title="Предложения" Width="1000" Height="600">
	<DockPanel LastChildFill="True">
		<!-- Шапка -->
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto"/>
				<ColumnDefinition Width="Auto"/>
				<ColumnDefinition Width="*"/>
				<ColumnDefinition Width="*"/>
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Click="BackButton_Click" Grid.Column="0" Margin="10">Назад</Button>
			<Button x:Name="AddButton" Click="AddButton_Click" Classes="outline-button" Grid.Column="1" Margin="10">Добавить предложение</Button>
			<Label Grid.Column="2" Margin="10" FontSize="36" HorizontalAlignment="Center">Предложения</Label>
			<Image Grid.Column="3" Classes="Logo"/>
		</Grid>

		<!-- Поиск по клиенту -->
		<controls:SearchTextBox
		  x:Name="SearchBox"
		  Watermark="Поиск по клиенту"
		  DockPanel.Dock="Top"
		  Margin="25,0"/>

		<!-- Таблица -->
		<DataGrid x:Name="SuppliesGrid"
				  AutoGenerateColumns="False"
				  IsReadOnly="True"
				  SelectionMode="Single"
				  Margin="10">
			<DataGrid.Columns>
				<DataGridTextColumn Header="Клиент"        Binding="{Binding ClientFullName}" />
				<DataGridTextColumn Header="Риэлтор"       Binding="{Binding AgentFullName}" />
				<DataGridTextColumn Header="Недвижимость"  Binding="{Binding RealEstateInfo}" Width="Auto" />
				<DataGridTextColumn Header="Цена"          Binding="{Binding Price}" />

				<DataGridTemplateColumn Header="Изменить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Изменить"
									Classes="outline-button"
									Click="EditButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Удалить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Удалить"
									Classes="delete-button"
									Click="DeleteButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>
			</DataGrid.Columns>
		</DataGrid>
	</DockPanel>
</Window>
