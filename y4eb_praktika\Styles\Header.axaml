<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <Design.PreviewWith>
    <Border Padding="20">
		<Label Classes="Header1">Hello World!</Label>
    </Border>
  </Design.PreviewWith>

  <!-- Header Styles -->
	<Style Selector="Label.Header1">
		<Setter Property="FontFamily" Value="Roboto"/>
		<Setter Property="FontSize" Value="36"/>
		<Setter Property="HorizontalAlignment" Value="Center"/>
	</Style>

	<!-- Modern Header Container -->
	<Style Selector="Grid.HeaderContainer">
		<Setter Property="Background" Value="#FFFFFF"/>
		<Setter Property="Height" Value="70"/>
		<Setter Property="Margin" Value="0"/>
		<Setter Property="Effect">
			<DropShadowEffect Color="#000000" Opacity="0.1" OffsetX="0" OffsetY="2" BlurRadius="8"/>
		</Setter>
		<Setter Property="ZIndex" Value="100"/>
	</Style>

	<!-- Logo and Brand Styles -->
	<Style Selector="Label.BrandTitle">
		<Setter Property="FontFamily" Value="Roboto"/>
		<Setter Property="FontSize" Value="24"/>
		<Setter Property="FontWeight" Value="Bold"/>
		<Setter Property="Foreground" Value="#0091ea"/>
		<Setter Property="VerticalAlignment" Value="Center"/>
		<Setter Property="Margin" Value="20,0,0,0"/>
	</Style>

	<!-- Navigation Container -->
	<Style Selector="StackPanel.NavContainer">
		<Setter Property="Orientation" Value="Horizontal"/>
		<Setter Property="HorizontalAlignment" Value="Right"/>
		<Setter Property="VerticalAlignment" Value="Center"/>
		<Setter Property="Margin" Value="0,0,20,0"/>
	</Style>

	<!-- Welcome Content Styles -->
	<Style Selector="Label.WelcomeTitle">
		<Setter Property="FontFamily" Value="Roboto"/>
		<Setter Property="FontSize" Value="42"/>
		<Setter Property="FontWeight" Value="Light"/>
		<Setter Property="Foreground" Value="#333333"/>
		<Setter Property="HorizontalAlignment" Value="Center"/>
		<Setter Property="Margin" Value="0,0,0,20"/>
	</Style>

	<Style Selector="TextBlock.WelcomeText">
		<Setter Property="FontFamily" Value="Roboto"/>
		<Setter Property="FontSize" Value="16"/>
		<Setter Property="Foreground" Value="#666666"/>
		<Setter Property="TextAlignment" Value="Center"/>
		<Setter Property="LineHeight" Value="1.8"/>
		<Setter Property="MaxWidth" Value="700"/>
		<Setter Property="TextWrapping" Value="Wrap"/>
		<Setter Property="HorizontalAlignment" Value="Center"/>
	</Style>

	<!-- Main Content Area -->
	<Style Selector="Grid.MainContent">
		<Setter Property="Background" Value="#F8F9FA"/>
	</Style>

	<!-- Responsive Navigation for smaller screens -->
	<Style Selector="StackPanel.NavContainer.Compact">
		<Setter Property="Orientation" Value="Vertical"/>
		<Setter Property="HorizontalAlignment" Value="Center"/>
		<Setter Property="VerticalAlignment" Value="Top"/>
		<Setter Property="Margin" Value="0,20,0,0"/>
	</Style>

	<Style Selector="StackPanel.NavContainer.Compact Button">
		<Setter Property="Margin" Value="5"/>
		<Setter Property="MinWidth" Value="200"/>
	</Style>

	<!-- Compact Brand Title -->
	<Style Selector="Label.BrandTitle.Compact">
		<Setter Property="FontSize" Value="20"/>
		<Setter Property="Margin" Value="10,0,0,0"/>
	</Style>

	<!-- Page Background -->
	<Style Selector="DockPanel.PageContainer">
		<Setter Property="Background" Value="#F8F9FA"/>
	</Style>

	<!-- Page Header -->
	<Style Selector="Grid.PageHeader">
		<Setter Property="Background" Value="White"/>
		<Setter Property="Margin" Value="20,20,20,10"/>
	</Style>

	<!-- Page Title -->
	<Style Selector="Label.PageTitle">
		<Setter Property="FontFamily" Value="Roboto"/>
		<Setter Property="FontSize" Value="28"/>
		<Setter Property="FontWeight" Value="SemiBold"/>
		<Setter Property="VerticalAlignment" Value="Center"/>
		<Setter Property="Foreground" Value="#333333"/>
	</Style>
</Styles>
