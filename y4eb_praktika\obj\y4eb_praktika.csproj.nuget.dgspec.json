{"format": 1, "restore": {"D:\\MyDiffFiles\\КИТ\\3 курс\\2 семестр\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika.csproj": {}}, "projects": {"D:\\MyDiffFiles\\КИТ\\3 курс\\2 семестр\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyDiffFiles\\КИТ\\3 курс\\2 семестр\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika.csproj", "projectName": "y4eb_praktika", "projectPath": "D:\\MyDiffFiles\\КИТ\\3 курс\\2 семестр\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyDiffFiles\\КИТ\\3 курс\\2 семестр\\y4eb_praktika\\y4eb_praktika\\y4eb_praktika\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.2.1, )"}, "Avalonia.Controls.DataGrid": {"target": "Package", "version": "[11.2.1, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.2.1, )"}, "Avalonia.Diagnostics": {"target": "Package", "version": "[11.2.1, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.2.1, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.2.1, )"}, "MessageBox.Avalonia": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Notification.Avalonia": {"target": "Package", "version": "[2.1.0, )"}, "System.Reactive": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}