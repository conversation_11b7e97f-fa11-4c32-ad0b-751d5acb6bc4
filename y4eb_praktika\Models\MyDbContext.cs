﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;



namespace y4eb_praktika.Models
{
    public class MyDbContext : DbContext
    {
        public DbSet<Agents> Agents { get; set; }
        public DbSet<Apartments> Apartments { get; set; }
        public DbSet<ApartmentsDemands> ApartmentsDemands { get; set; }
        public DbSet<Clients> Clients { get; set; }
        public DbSet<Deals> Deals { get; set; }
        public DbSet<Demands> Demands { get; set; }
        public DbSet<Districts> Districts { get; set; }
        public DbSet<HouseDemands> HouseDemands { get; set; }
        public DbSet<Houses> Houses { get; set; }
        public DbSet<LandDemands> LandDemands { get; set; }
        public DbSet<Lands> Lands { get; set; }
        public DbSet<RealEstates> RealEstates { get; set; }
        public DbSet<RealEstateTypes> RealEstateTypes { get; set; }
        public DbSet<Supplies> Supplies { get; set; }


        public MyDbContext() { }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if(!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlServer("Data Source=localhost;Initial Catalog=YchebPraktika;Persist Security Info=True;User ID=sa;Password=YourStrongPassword!;Encrypt=True;Trust Server Certificate=True");
            }
        }
       public static MyDbContext _context;
        public static MyDbContext GetContext()
        {
            return new MyDbContext();
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {

            modelBuilder.Entity<Supplies>()
                .HasOne(s => s.Deal)
                .WithOne(d => d.Supply)
                .HasForeignKey<Deals>(d => d.Supply_Id)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<Demands>()
                .HasOne(d => d.Deal)
                .WithOne(d => d.Demand)
                .HasForeignKey<Deals>(d => d.Demand_Id)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<RealEstates>()
                .HasOne(r => r.Apartment)
                .WithMany()
                .HasForeignKey(r => r.ApartmentId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<RealEstates>()
                .HasOne(r => r.House)
                .WithMany()
                .HasForeignKey(r => r.HouseId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<RealEstates>()
                .HasOne(r => r.Land)
                .WithMany()
                .HasForeignKey(r => r.LandId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<RealEstates>()
                .HasOne(r => r.District)
                .WithMany()
                .HasForeignKey(r => r.DistrictId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Demands>()
                .HasOne(d => d.ApartmentDemand)
                .WithMany()
                .HasForeignKey(d => d.ApartmentDemandId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Demands>()
                .HasOne(d => d.HouseDemand)
                .WithMany()
                .HasForeignKey(d => d.HouseDemandId)
                .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Demands>()
                .HasOne(d => d.LandDemand)
                .WithMany()
                .HasForeignKey(d => d.LandDemandId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }

}
