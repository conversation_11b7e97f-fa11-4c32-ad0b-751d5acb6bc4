<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
        xmlns:controls="clr-namespace:y4eb_praktika"
        xmlns:models="clr-namespace:y4eb_praktika.Models"
        x:CompileBindings="False"
		
        x:Class="y4eb_praktika.AgentsWindow"
        Title="AgentsWindow">
	<DockPanel LastChildFill="True">
		<Grid DockPanel.Dock="Top" Margin="0,0,0,10">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="*" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Click="BackButton_Click" Grid.Column="0" Margin="10">Назад</Button>
			<Button x:Name="AddButton" Click="AddButton_Click" Classes="outline-button" Grid.Column="1" Margin="10">Добавить риэлтора</Button>
			<Label Grid.Column="2" Margin="10" FontSize="36" HorizontalAlignment="Center">Риэлторы</Label>
			<Image Grid.Column="3" Classes="Logo"/>
		</Grid>

		<controls:SearchTextBox
		  x:Name="SearchBox"
		  Watermark="Поиск риэлторов"
		  DockPanel.Dock="Top"
		  Margin="25,0"/>

		<DataGrid x:Name="DataGridAgents"
				  ItemsSource="{Binding AgentsList}"
				  AutoGenerateColumns="False"
				  CanUserSortColumns="True"
				  Margin="25">
			<DataGrid.Columns>
				<DataGridTextColumn Header="ИД"       Binding="{Binding Id}"         Width="Auto"/>
				<DataGridTextColumn Header="Фамилия"      Binding="{Binding FirstName}"  Width="Auto"/>
				<DataGridTextColumn Header="Имя"  Binding="{Binding MiddleName}" Width="Auto"/>
				<DataGridTextColumn Header="Отчество" Binding="{Binding LastName}"   Width="Auto"/>
				<DataGridTextColumn Header="Доля от комиссии"  Binding="{Binding DealShare}"      Width="Auto"/>

				<DataGridTemplateColumn Header="Изменить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Изменить"
									Classes="outline-button"
									Click="EditButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Удалить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Удалить"
									Classes="delete-button"
									Click="DeleteButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Подробнее" Width="Auto">
							<DataGridTemplateColumn.CellTemplate>
								<DataTemplate>
									<Button Content="Подробнее"
											Classes="outline-button"
											Click="MoreButton_Click"
											Tag="{Binding}" />
								</DataTemplate>
							</DataGridTemplateColumn.CellTemplate>
						</DataGridTemplateColumn>
			</DataGrid.Columns>
		</DataGrid>
	</DockPanel>
</Window>
