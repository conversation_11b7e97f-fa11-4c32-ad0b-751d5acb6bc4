using Avalonia.Controls;
using Avalonia.Controls.Notifications;
using Avalonia.Interactivity;
using System;
using System.Globalization;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using y4eb_praktika.Models;

namespace y4eb_praktika.Pages.Supplies
{
    public partial class SupplyEditWindow : Window
    {
        private readonly y4eb_praktika.Models.Supplies _supply;
        private readonly WindowNotificationManager _notif;

        public SupplyEditWindow(y4eb_praktika.Models.Supplies supply)
        {
            InitializeComponent();
            _supply = supply;

            _notif = new WindowNotificationManager(this)
            {
                Position = NotificationPosition.BottomLeft,
                MaxItems = 3
            };

            LoadLookups();
            PopulateFields();
        }

        private void LoadLookups()
        {
            using var db = MyDbContext.GetContext();

            // �������
            var clients = db.Clients
                .Select(c => new { c.Id, Full = $"{c.MiddleName} {c.LastName}".Trim() })
                .ToList();
            ClientComboBox.ItemsSource = clients;
            ClientComboBox.SelectedIndex = -1;

            // ��������
            var agents = db.Agents
                .Select(a => new { a.Id, Full = $"{a.MiddleName} {a.LastName}".Trim() })
                .ToList();
            AgentComboBox.ItemsSource = agents;
            AgentComboBox.SelectedIndex = -1;

            // ������������ (���������� ��� ��� ����, �� ������� ���������� ������ ������ � ������)
            var apartments = db.RealEstates
                .Include(r => r.Apartment)
                .Where(r => r.Apartment != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"��.#{r.Apartment.Id}: {r.Apartment.Address_City}, {r.Apartment.Address_Street} {r.Apartment.Address_House}, ��.{r.Apartment.Address_Number}"
                })
                .ToList(); // <- �����

            var houses = db.RealEstates
                .Include(r => r.House)
                .Where(r => r.House != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"���#{r.House.Id}: {r.House.Address_City}, {r.House.Address_Street} {r.House.Address_House}"
                })
                .ToList(); // <- � �����

            var lands = db.RealEstates
                .Include(r => r.Land)
                .Where(r => r.Land != null)
                .Select(r => new {
                    r.Id,
                    Desc = $"�����#{r.Land.Id}: {r.Land.Address_City}, {r.Land.Address_Street} {r.Land.Address_House}"
                })
                .ToList(); // <- � �����

            // ������ Concat ��� �������� ��� �������� � ������
            var all = apartments
                .Concat(houses)
                .Concat(lands)
                .ToList();

            RealEstateComboBox.ItemsSource = all;
            RealEstateComboBox.SelectedIndex = -1;
        }


        private void PopulateFields()
        {
            IdLabel.Text = _supply.Id.ToString();
            // ���������� ��������� �������� �� Id
            dynamic cli = ClientComboBox.ItemsSource.Cast<dynamic>()
                              .FirstOrDefault(x => x.Id == _supply.ClientId);
            ClientComboBox.SelectedItem = cli;

            dynamic ag = AgentComboBox.ItemsSource.Cast<dynamic>()
                              .FirstOrDefault(x => x.Id == _supply.AgentId);
            AgentComboBox.SelectedItem = ag;

            dynamic re = RealEstateComboBox.ItemsSource.Cast<dynamic>()
                              .FirstOrDefault(x => x.Id == _supply.RealEstateId);
            RealEstateComboBox.SelectedItem = re;

            PriceTextBox.Text = _supply.Price.ToString(CultureInfo.InvariantCulture);
        }

        private void ClearErrors()
        {
            foreach (var tb in new[] { ClientError, AgentError, RealEstateError, PriceError })
                tb.IsVisible = false;
        }

        private void SetError(TextBlock err, string msg)
        {
            err.Text = msg;
            err.IsVisible = true;
        }

        private bool TryParsePrice(out int price)
        {
            price = 0;
            if (!int.TryParse(PriceTextBox.Text, NumberStyles.Integer, CultureInfo.InvariantCulture, out price) || price <= 0)
            {
                SetError(PriceError, "������� ����� ������������� �����");
                return false;
            }
            return true;
        }

        private void SaveButton_Click(object? sender, RoutedEventArgs e)
        {
            ClearErrors();
            bool hasError = false;

            dynamic cli = ClientComboBox.SelectedItem;
            if (cli == null) { SetError(ClientError, "�������� �������"); hasError = true; }

            dynamic ag = AgentComboBox.SelectedItem;
            if (ag == null) { SetError(AgentError, "�������� ��������"); hasError = true; }

            dynamic re = RealEstateComboBox.SelectedItem;
            if (re == null) { SetError(RealEstateError, "�������� ������"); hasError = true; }

            if (!TryParsePrice(out var price)) hasError = true;

            if (hasError) return;

            using var db = MyDbContext.GetContext();
            var entity = db.Supplies.FirstOrDefault(s => s.Id == _supply.Id);
            if (entity == null) return;

            entity.ClientId = (int)cli.Id;
            entity.AgentId = (int)ag.Id;
            entity.RealEstateId = (int)re.Id;
            entity.Price = price;

            try
            {
                db.SaveChanges();
                _notif.Show(new Notification("�����", "����������� ���������", NotificationType.Success));
                this.Close(true);
            }
            catch (DbUpdateException ex)
            {
                // ���� ����������� ��������� � ������, EF ����� ������� ���������� FK
                _notif.Show(new Notification("������", "������ �������� �����������, ����������� � ������", NotificationType.Error));
            }
        }

        private void BackButton_Click(object? sender, RoutedEventArgs e)
        {
            this.Close(false);
        }
    }
}
