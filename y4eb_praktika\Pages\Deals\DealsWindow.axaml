<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        xmlns:controls="clr-namespace:y4eb_praktika"
        xmlns:vm="clr-namespace:y4eb_praktika.Pages.Deals"
        xmlns:components="clr-namespace:y4eb_praktika.Components"
        x:Class="y4eb_praktika.Pages.Deals.DealsWindow"
        x:CompileBindings="False"
        Title="Агентство недвижимости - Сделки"
        MinWidth="800" MinHeight="450" Width="1200" Height="700">
	<DockPanel LastChildFill="True" Classes="PageContainer">
		<!-- Navigation Header -->
		<components:NavigationHeader DockPanel.Dock="Top"/>

		<!-- <PERSON> Header -->
		<Grid DockPanel.Dock="Top" Classes="PageHeader">
			<Grid.ColumnDefinitions>
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="Auto" />
				<ColumnDefinition Width="*" />
				<ColumnDefinition Width="Auto" />
			</Grid.ColumnDefinitions>
			<Button x:Name="BackButton" Click="BackButton_Click" Grid.Column="0" Margin="0,0,15,0">Назад</Button>
			<Button x:Name="AddButton" Click="AddButton_Click" Classes="outline-button" Grid.Column="1" Margin="0,0,15,0">Добавить сделку</Button>
			<Label Grid.Column="2" Classes="PageTitle">Сделки</Label>
			<Button x:Name="SearchButton" Click="SearchButton_Click" Classes="outline-button" Grid.Column="3">Найти подходящие предложения</Button>
		</Grid>

		<!-- Поиск по клиенту-продавцу -->
		<controls:SearchTextBox
		  x:Name="SearchBox"
		  Watermark="Поиск по клиенту-продавцу"
		  DockPanel.Dock="Top"
		  Margin="20,0,20,15"/>

		<!-- Таблица сделок -->
		<DataGrid x:Name="DealsGrid"
				  AutoGenerateColumns="False"
				  IsReadOnly="True"
				  SelectionMode="Single"
				  Margin="20,0,20,20"
				  Background="White"
				  BorderBrush="#E0E0E0"
				  BorderThickness="1"
				  CornerRadius="8">
			<DataGrid.Columns>
				<DataGridTextColumn Header="Клиент-продавец"    Binding="{Binding SellerClient}" />
				<DataGridTextColumn Header="Агент-продавца"      Binding="{Binding SellerAgent}" />
				<DataGridTextColumn Header="Клиент-покупатель"   Binding="{Binding BuyerClient}" />
				<DataGridTextColumn Header="Агент-покупателя"    Binding="{Binding BuyerAgent}" />
				<DataGridTextColumn Header="Объект"              Binding="{Binding RealEstateInfo}" Width="Auto" />
				<DataGridTextColumn Header="Цена (продажи)"      Binding="{Binding SalePrice}" />
				<DataGridTextColumn Header="Комиссия продавца"   Binding="{Binding SellerCommission}" />
				<DataGridTextColumn Header="Комиссия покупателя" Binding="{Binding BuyerCommission}" />
				<DataGridTextColumn Header="Риэлтор-продавец"    Binding="{Binding SellerRealtorShare}" />
				<DataGridTextColumn Header="Риэлтор-покупатель"  Binding="{Binding BuyerRealtorShare}" />
				<DataGridTextColumn Header="Компания"            Binding="{Binding CompanyShare}" />

				<DataGridTemplateColumn Header="Изменить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Изменить"
									Classes="outline-button"
									Click="EditButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>

				<DataGridTemplateColumn Header="Удалить" Width="Auto">
					<DataGridTemplateColumn.CellTemplate>
						<DataTemplate>
							<Button Content="Удалить"
									Classes="delete-button"
									Click="DeleteButton_Click"
									Tag="{Binding}" />
						</DataTemplate>
					</DataGridTemplateColumn.CellTemplate>
				</DataGridTemplateColumn>
			</DataGrid.Columns>
		</DataGrid>
	</DockPanel>
</Window>
