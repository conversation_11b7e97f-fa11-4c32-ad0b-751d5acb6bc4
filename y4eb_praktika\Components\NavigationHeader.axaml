<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="70"
             x:Class="y4eb_praktika.Components.NavigationHeader">
	
	<!-- Modern Header -->
	<Grid Classes="HeaderContainer">
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="Auto"/>
			<ColumnDefinition Width="*"/>
			<ColumnDefinition Width="Auto"/>
		</Grid.ColumnDefinitions>
		
		<!-- Logo -->
		<StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
			<Image Classes="Logo" Width="50" Height="50" Margin="20,0,0,0"/>
		</StackPanel>
		
		<!-- Navigation Menu -->
		<StackPanel Grid.Column="2" Classes="NavContainer">
			<Button x:Name="AgentButton" Classes="NavButton" Click="AgentButton_Click">Агенты</Button>
			<Button x:Name="RealEstateButton" Classes="NavButton" Click="RealEstateButton_Click">Недвижимость</Button>
			<Button x:Name="ClientButton" Classes="NavButton" Click="ClientButton_Click">Клиенты</Button>
			<Button x:Name="DemandsButton" Classes="NavButton" Click="DemandButton_Click">Потребности</Button>
			<Button x:Name="DealsButton" Classes="NavButton" Click="DealsButton_Click">Сделки</Button>
			<Button x:Name="SupliesButton" Classes="NavButton" Click="SupliesButton_Click">Предложения</Button>
		</StackPanel>
	</Grid>
	
</UserControl>
