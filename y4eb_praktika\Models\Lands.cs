﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace y4eb_praktika.Models
{
    public class Lands
    {
        [Key]
        public int Id {  get; set; }
        public string? Address_City { get; set; }
        public string? Address_Street { get; set; }
        public string? Address_House { get; set; }
        public string? Address_Number { get; set; }

        public double? Coordinate_latitude { get; set; }
        public double? Coordinate_longitude { get; set; }

        public double? TotalArea { get; set; }
    }
}
